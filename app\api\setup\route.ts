import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import User from "@/lib/models/user"
import bcrypt from "bcryptjs"

export async function GET() {
  try {
    console.log("Setup API - Environment Info:")
    console.log("- NODE_ENV:", process.env.NODE_ENV)
    console.log("- MONGODB_URI exists:", !!process.env.MONGODB_URI)
    console.log("- MONGODB_URI preview:", process.env.MONGODB_URI?.substring(0, 20) + "...")

    // Veritabanına bağlan
    console.log("Setup API - Connecting to database...")
    await dbConnect()
    console.log("Setup API - Database connection successful")

    // Kullanıcı var mı kontrol et
    const existingUser = await User.findOne({ email: "<EMAIL>" })

    if (existingUser) {
      return NextResponse.json({
        success: true,
        message: "<PERSON><PERSON> kullanıc<PERSON>sı zaten mevcut",
        user: {
          name: existingUser.name,
          email: existingUser.email,
          role: existingUser.role,
        },
      })
    }

    // <PERSON><PERSON>reyi hashle
    const salt = await bcrypt.genSalt(10)
    const hashedPassword = await bcrypt.hash("admin123", salt)

    // Yeni admin kullanıcısı oluştur
    const newUser = await User.create({
      name: "Admin Kullanıcı",
      email: "<EMAIL>",
      password: hashedPassword,
      role: "admin",
      isActive: true,
    })

    return NextResponse.json({
      success: true,
      message: "Admin kullanıcısı başarıyla oluşturuldu",
      user: {
        name: newUser.name,
        email: newUser.email,
        role: newUser.role,
      },
    })
  } catch (error) {
    console.error("Setup error:", error)
    return NextResponse.json({ success: false, error: "Kurulum sırasında bir hata oluştu" }, { status: 500 })
  }
}
