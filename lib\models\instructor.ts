import mongoose from "mongoose"

// Define the schema
const instructorSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Eğitmen adı gereklidir"],
      trim: true,
    },
    role: {
      type: String,
      trim: true,
    },
    bio: {
      type: String,
      trim: true,
    },
    image: {
      type: String,
      trim: true,
    },
    expertise: {
      type: String,
      trim: true,
    },
    experience: {
      type: String,
      trim: true,
    },
    email: {
      type: String,
      trim: true,
    },
    phone: {
      type: String,
      trim: true,
    },
    social: {
      linkedin: {
        type: String,
        trim: true,
      },
      twitter: {
        type: String,
        trim: true,
      },
      website: {
        type: String,
        trim: true,
      },
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  },
)

// Check if the model already exists to prevent overwriting
export default mongoose.models.Instructor || mongoose.model("Instructor", instructorSchema)
