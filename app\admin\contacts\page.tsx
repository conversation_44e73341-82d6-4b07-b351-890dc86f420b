"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Trash2, Eye, Search, Mail, Phone, Calendar } from "lucide-react"
import { toast } from "sonner"

export default function ContactsPage() {
  const [contacts, setContacts] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedContact, setSelectedContact] = useState(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [filters, setFilters] = useState({
    status: "all",
    search: "",
  })

  useEffect(() => {
    fetchContacts()
  }, [filters])

  const fetchContacts = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (filters.status !== "all") params.append("status", filters.status)
      if (filters.search) params.append("search", filters.search)

      const response = await fetch(`/api/admin/contacts?${params}`)
      const data = await response.json()

      if (data.success) {
        setContacts(data.data)
      } else {
        toast.error(data.error || "Mesajlar yüklenirken hata oluştu")
      }
    } catch (error) {
      console.error("Error fetching contacts:", error)
      toast.error("Mesajlar yüklenirken hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  const handleView = async (contact) => {
    setSelectedContact(contact)
    setIsDialogOpen(true)

    // Mark as read
    if (!contact.isRead) {
      try {
        await fetch(`/api/admin/contacts/${contact._id}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ isRead: true }),
        })
        fetchContacts()
      } catch (error) {
        console.error("Error marking as read:", error)
      }
    }
  }

  const handleDelete = async (id) => {
    if (!confirm("Bu mesajı silmek istediğinizden emin misiniz?")) return

    try {
      const response = await fetch(`/api/admin/contacts/${id}`, {
        method: "DELETE",
      })

      const data = await response.json()

      if (data.success) {
        toast.success("Mesaj başarıyla silindi")
        fetchContacts()
      } else {
        toast.error(data.error || "Mesaj silinirken hata oluştu")
      }
    } catch (error) {
      console.error("Error deleting contact:", error)
      toast.error("Mesaj silinirken hata oluştu")
    }
  }

  const handleStatusChange = async (id, status) => {
    try {
      const response = await fetch(`/api/admin/contacts/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ status }),
      })

      const data = await response.json()

      if (data.success) {
        toast.success("Durum güncellendi")
        fetchContacts()
      } else {
        toast.error(data.error || "Durum güncellenirken hata oluştu")
      }
    } catch (error) {
      console.error("Error updating status:", error)
      toast.error("Durum güncellenirken hata oluştu")
    }
  }

  const getStatusBadge = (status) => {
    const statusConfig = {
      new: { label: "Yeni", variant: "default" },
      read: { label: "Okundu", variant: "secondary" },
      replied: { label: "Yanıtlandı", variant: "success" },
      closed: { label: "Kapatıldı", variant: "outline" },
    }
    const config = statusConfig[status] || statusConfig.new
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">İletişim Mesajları</h1>
          <p className="text-muted-foreground">Kullanıcılardan gelen mesajları yönetin</p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filtreler</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Ad, e-posta veya konu ara..."
                  value={filters.search}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={filters.status} onValueChange={(value) => setFilters({ ...filters, status: value })}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Durum seçin" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tümü</SelectItem>
                <SelectItem value="new">Yeni</SelectItem>
                <SelectItem value="read">Okundu</SelectItem>
                <SelectItem value="replied">Yanıtlandı</SelectItem>
                <SelectItem value="closed">Kapatıldı</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Contacts List */}
      <Card>
        <CardHeader>
          <CardTitle>Mesajlar ({contacts.length})</CardTitle>
          <CardDescription>Kullanıcılardan gelen iletişim mesajları</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-20 bg-gray-200 rounded animate-pulse" />
              ))}
            </div>
          ) : contacts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Mail className="mx-auto h-12 w-12 mb-4" />
              <p>Henüz mesaj bulunmuyor</p>
            </div>
          ) : (
            <div className="space-y-4">
              {contacts.map((contact) => (
                <div
                  key={contact._id}
                  className={`p-4 border rounded-lg ${!contact.isRead ? "bg-blue-50 border-blue-200" : ""}`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-semibold">{contact.name}</h3>
                        {getStatusBadge(contact.status)}
                        {!contact.isRead && <Badge variant="destructive">Okunmadı</Badge>}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{contact.subject}</p>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <Mail className="h-3 w-3" />
                          {contact.email}
                        </span>
                        {contact.phone && (
                          <span className="flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            {contact.phone}
                          </span>
                        )}
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(contact.createdAt).toLocaleDateString("tr-TR")}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Select
                        value={contact.status}
                        onValueChange={(value) => handleStatusChange(contact._id, value)}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="new">Yeni</SelectItem>
                          <SelectItem value="read">Okundu</SelectItem>
                          <SelectItem value="replied">Yanıtlandı</SelectItem>
                          <SelectItem value="closed">Kapatıldı</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button variant="outline" size="sm" onClick={() => handleView(contact)}>
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleDelete(contact._id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* View Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Mesaj Detayı</DialogTitle>
          </DialogHeader>
          {selectedContact && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Ad Soyad</label>
                  <p className="text-sm text-muted-foreground">{selectedContact.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">E-posta</label>
                  <p className="text-sm text-muted-foreground">{selectedContact.email}</p>
                </div>
                {selectedContact.phone && (
                  <div>
                    <label className="text-sm font-medium">Telefon</label>
                    <p className="text-sm text-muted-foreground">{selectedContact.phone}</p>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium">Tarih</label>
                  <p className="text-sm text-muted-foreground">
                    {new Date(selectedContact.createdAt).toLocaleString("tr-TR")}
                  </p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium">Konu</label>
                <p className="text-sm text-muted-foreground">{selectedContact.subject}</p>
              </div>
              <div>
                <label className="text-sm font-medium">Mesaj</label>
                <div className="mt-2 p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm whitespace-pre-wrap">{selectedContact.message}</p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
