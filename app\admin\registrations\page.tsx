"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { useToast } from "@/components/ui/use-toast"
import { Search, Filter, Check, X, Eye } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

export default function RegistrationsPage() {
  const { toast } = useToast()
  const [registrations, setRegistrations] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [programFilter, setProgramFilter] = useState("all")
  const [selectedRegistration, setSelectedRegistration] = useState(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const fetchRegistrations = async () => {
    try {
      setIsLoading(true)
      const params = new URLSearchParams()
      if (statusFilter !== "all") params.append("status", statusFilter)
      if (programFilter !== "all") params.append("program", programFilter)
      if (searchTerm) params.append("search", searchTerm)

      const response = await fetch(`/api/admin/registrations?${params}`)
      const data = await response.json()

      if (data.success) {
        setRegistrations(data.data)
      } else {
        console.error("Error fetching registrations:", data.error)
        toast({
          title: "Hata",
          description: "Kayıtlar yüklenirken bir hata oluştu.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error fetching registrations:", error)
      toast({
        title: "Hata",
        description: "Kayıtlar yüklenirken bir hata oluştu.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchRegistrations()
  }, [statusFilter, programFilter, searchTerm])

  const handleViewDetails = (registration) => {
    setSelectedRegistration(registration)
    setIsDialogOpen(true)
  }

  const handleUpdateStatus = async (id, newStatus) => {
    try {
      const response = await fetch(`/api/admin/registrations/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      })

      const data = await response.json()

      if (data.success) {
        setRegistrations((prev) => prev.map((reg) => (reg._id === id ? { ...reg, status: newStatus } : reg)))
        toast({
          title: "Durum Güncellendi",
          description: `Kayıt durumu ${newStatus === "approved" ? "onaylandı" : "reddedildi"}.`,
        })
        setIsDialogOpen(false)
      } else {
        toast({
          title: "Hata",
          description: data.error || "Durum güncellenirken bir hata oluştu.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error updating status:", error)
      toast({
        title: "Hata",
        description: "Durum güncellenirken bir hata oluştu.",
        variant: "destructive",
      })
    }
  }

  const filteredRegistrations = registrations.filter((reg) => {
    const matchesSearch =
      reg.childName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      reg.parentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      reg.email.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || reg.status === statusFilter
    const matchesProgram = programFilter === "all" || reg.program === programFilter

    return matchesSearch && matchesStatus && matchesProgram
  })

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("tr-TR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  const getProgramName = (programCode) => {
    switch (programCode) {
      case "ua1":
        return "UA1 - Giriş Seviyesi"
      case "ua2":
        return "UA2 - Orta Seviye"
      case "ua3":
        return "UA3 - İleri Seviye"
      default:
        return programCode
    }
  }

  return (
    <div>
      <h1 className="text-3xl font-bold mb-8">Kayıtlar</h1>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Kayıt Listesi</CardTitle>
          <CardDescription>Tüm kayıt başvurularını görüntüleyin ve yönetin.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="İsim veya e-posta ile ara..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <div className="w-40">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <div className="flex items-center gap-2">
                      <Filter className="h-4 w-4" />
                      <SelectValue placeholder="Durum" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Durumlar</SelectItem>
                    <SelectItem value="pending">Bekleyen</SelectItem>
                    <SelectItem value="approved">Onaylanan</SelectItem>
                    <SelectItem value="rejected">Reddedilen</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-40">
                <Select value={programFilter} onValueChange={setProgramFilter}>
                  <SelectTrigger>
                    <div className="flex items-center gap-2">
                      <Filter className="h-4 w-4" />
                      <SelectValue placeholder="Program" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Programlar</SelectItem>
                    <SelectItem value="ua1">UA1 - Giriş Seviyesi</SelectItem>
                    <SelectItem value="ua2">UA2 - Orta Seviye</SelectItem>
                    <SelectItem value="ua3">UA3 - İleri Seviye</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="text-center py-8">
              <p>Yükleniyor...</p>
            </div>
          ) : filteredRegistrations.length === 0 ? (
            <div className="text-center py-8">
              <p>Kayıt bulunamadı.</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Çocuk Adı</TableHead>
                    <TableHead>Veli Adı</TableHead>
                    <TableHead>Program</TableHead>
                    <TableHead>Tarih</TableHead>
                    <TableHead>Durum</TableHead>
                    <TableHead className="text-right">İşlemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRegistrations.map((registration) => (
                    <TableRow key={registration._id || registration.id}>
                      <TableCell className="font-medium">{registration.childName}</TableCell>
                      <TableCell>{registration.parentName}</TableCell>
                      <TableCell>{getProgramName(registration.program)}</TableCell>
                      <TableCell>{formatDate(registration.createdAt)}</TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 text-xs rounded-full ${
                            registration.status === "pending"
                              ? "bg-yellow-100 text-yellow-800"
                              : registration.status === "approved"
                                ? "bg-green-100 text-green-800"
                                : "bg-red-100 text-red-800"
                          }`}
                        >
                          {registration.status === "pending"
                            ? "Bekliyor"
                            : registration.status === "approved"
                              ? "Onaylandı"
                              : "Reddedildi"}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" onClick={() => handleViewDetails(registration)}>
                          <Eye className="h-4 w-4 mr-1" />
                          Detay
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <p className="text-sm text-gray-500">Toplam {filteredRegistrations.length} kayıt</p>
        </CardFooter>
      </Card>

      {/* Registration Details Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Kayıt Detayları</DialogTitle>
            <DialogDescription>Kayıt başvurusunun detaylarını görüntüleyin ve durumunu güncelleyin.</DialogDescription>
          </DialogHeader>
          {selectedRegistration && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <p className="text-sm font-medium text-gray-500">Çocuğun Adı</p>
                  <p>{selectedRegistration.childName}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Çocuğun Yaşı</p>
                  <p>{selectedRegistration.childAge}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Veli Adı</p>
                  <p>{selectedRegistration.parentName}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">E-posta</p>
                  <p>{selectedRegistration.email}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Telefon</p>
                  <p>{selectedRegistration.phone}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Program</p>
                  <p>{getProgramName(selectedRegistration.program)}</p>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Adres</p>
                <p>{selectedRegistration.address}</p>
              </div>
              {selectedRegistration.notes && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Notlar</p>
                  <p>{selectedRegistration.notes}</p>
                </div>
              )}
              <div>
                <p className="text-sm font-medium text-gray-500">Durum</p>
                <span
                  className={`px-2 py-1 text-xs rounded-full ${
                    selectedRegistration.status === "pending"
                      ? "bg-yellow-100 text-yellow-800"
                      : selectedRegistration.status === "approved"
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                  }`}
                >
                  {selectedRegistration.status === "pending"
                    ? "Bekliyor"
                    : selectedRegistration.status === "approved"
                      ? "Onaylandı"
                      : "Reddedildi"}
                </span>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Kayıt Tarihi</p>
                <p>{formatDate(selectedRegistration.createdAt)}</p>
              </div>
            </div>
          )}
          <DialogFooter className="flex justify-between">
            {selectedRegistration && selectedRegistration.status === "pending" && (
              <>
                <Button
                  variant="outline"
                  className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
                  onClick={() => handleUpdateStatus(selectedRegistration._id || selectedRegistration.id, "rejected")}
                >
                  <X className="h-4 w-4 mr-1" />
                  Reddet
                </Button>
                <Button
                  className="bg-green-600 hover:bg-green-700"
                  onClick={() => handleUpdateStatus(selectedRegistration._id || selectedRegistration.id, "approved")}
                >
                  <Check className="h-4 w-4 mr-1" />
                  Onayla
                </Button>
              </>
            )}
            {selectedRegistration && selectedRegistration.status !== "pending" && (
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Kapat
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
