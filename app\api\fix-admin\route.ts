import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import User from "@/lib/models/user"
import bcrypt from "bcryptjs"

export async function GET() {
  try {
    console.log("=== FIX ADMIN PASSWORD ===")
    
    // Database bağlantısı
    await dbConnect()
    console.log("Database connected")
    
    // Mevcut admin kullanıcısını sil
    await User.deleteOne({ email: "<EMAIL>" })
    console.log("Old admin user deleted")
    
    // Yeni şifreyi hash'le
    const salt = await bcrypt.genSalt(10)
    const hashedPassword = await bcrypt.hash("admin123", salt)
    console.log("Password hashed:", hashedPassword.substring(0, 20) + "...")
    
    // Yeni admin kullanıcısı oluştur
    const newAdmin = await User.create({
      name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      password: hashedPassword,
      role: "admin",
      isActive: true
    })
    
    console.log("New admin created:", newAdmin.email)
    
    // Şifreyi test et
    const testResult = await newAdmin.comparePassword("admin123")
    console.log("Password test result:", testResult)
    
    return NextResponse.json({
      success: true,
      message: "Admin user recreated successfully",
      debug: {
        userCreated: true,
        email: newAdmin.email,
        role: newAdmin.role,
        passwordTest: testResult,
        passwordHash: hashedPassword.substring(0, 20) + "..."
      }
    })
    
  } catch (error) {
    console.error("Fix admin error:", error)
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 })
  }
}
