import "@/app/globals.css"
import { Inter } from "next/font/google"
import { ThemeProvider } from "@/components/theme-provider"
import { MainNav } from "@/components/layout/main-nav"
import { SiteFooter } from "@/components/layout/site-footer"
import { SessionProvider } from "@/components/providers/session-provider"
import { Toaster } from "@/components/ui/toaster"
import { DynamicFavicon } from "@/components/dynamic-favicon"
import "@/lib/console-filter" // Global console filter

const inter = Inter({ subsets: ["latin"] })

export const metadata = {
  title: "Uçuş Atölyesi - Drone ve Havacılık Eğitim Merkezi",
  description: "Çocuklar için STEM tabanlı drone ve havacılık eğitim merkezi",
  generator: 'Next.js',
  metadataBase: new URL('https://www.ucusatolyesi.com.tr'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "Uçuş Atölyesi - Drone ve Havacılık Eğitim Merkezi",
    description: "Çocuklar için STEM tabanlı drone ve havacılık eğitim merkezi",
    url: 'https://www.ucusatolyesi.com.tr',
    siteName: 'Uçuş Atölyesi',
    locale: 'tr_TR',
    type: 'website',
    images: [
      {
        url: '/logo.jpg',
        width: 1200,
        height: 630,
        alt: 'Uçuş Atölyesi Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: "Uçuş Atölyesi - Drone ve Havacılık Eğitim Merkezi",
    description: "Çocuklar için STEM tabanlı drone ve havacılık eğitim merkezi",
    images: ['/logo.jpg'],
  },
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: '32x32', type: 'image/x-icon' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      { rel: 'mask-icon', url: '/safari-pinned-tab.svg', color: '#0ea5e9' },
    ],
  },
  manifest: '/site.webmanifest',
}

export default function RootLayout({ children }) {
  return (
    <html lang="tr" suppressHydrationWarning>
      <body className={inter.className}>
        <SessionProvider>
          <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
            <DynamicFavicon />
            <div className="flex min-h-screen flex-col">
              <MainNav />
              <main className="flex-1">{children}</main>
              <SiteFooter />
            </div>
            <Toaster />
          </ThemeProvider>
        </SessionProvider>
      </body>
    </html>
  )
}
