import mongoose from "mongoose"

// Define the schema
const gallerySchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Başlık gereklidir"],
      trim: true,
    },
    category: {
      type: String,
      trim: true,
    },
    type: {
      type: String,
      enum: ["image", "video"],
      required: [true, "Tip gereklidir"],
      default: "image",
    },
    image: {
      type: String,
      trim: true,
    },
    videoUrl: {
      type: String,
      trim: true,
    },
    videoId: {
      type: String,
      trim: true,
    },
    videoTitle: {
      type: String,
      trim: true,
    },
    videoDescription: {
      type: String,
      trim: true,
    },
    videoThumbnail: {
      type: String,
      trim: true,
    },
    videoDuration: {
      type: String,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  },
)

// Check if the model already exists to prevent overwriting
export default mongoose.models.Gallery || mongoose.model("Gallery", gallerySchema)
