import type { NextAuthOptions } from "next-auth"
import Credential<PERSON><PERSON>rovider from "next-auth/providers/credentials"
import dbConnect from "./db-connect"
import User from "./models/user"
import { mockAuthenticateUser } from "./mock-auth"

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          console.log("Missing credentials")
          return null
        }

        console.log("Attempting login for:", credentials.email)

        try {
          // Environment debug bilgileri
          console.log("Auth Environment Info:")
          console.log("- NODE_ENV:", process.env.NODE_ENV)
          console.log("- MONGODB_URI exists:", !!process.env.MONGODB_URI)
          console.log("- NEXTAUTH_SECRET exists:", !!process.env.NEXTAUTH_SECRET)
          console.log("- NEXTAUTH_URL:", process.env.NEXTAUTH_URL)

          // Development modunda basit auth
          if (process.env.NODE_ENV === "development") {
            console.log("Development mode - using simple auth")
            if (credentials.email === "<EMAIL>" && credentials.password === "admin123") {
              console.log("Development login successful")
              return {
                id: "dev-admin-id",
                name: "Admin",
                email: "<EMAIL>",
                role: "admin",
              }
            } else {
              console.log("Development login failed")
              return null
            }
          }

          // MongoDB bağlantısı yoksa sahte kimlik doğrulama kullan
          if (!process.env.MONGODB_URI) {
            console.log("Using mock authentication")
            return await mockAuthenticateUser(credentials.email, credentials.password)
          }

          // MongoDB bağlantısı varsa gerçek kimlik doğrulama kullan
          console.log("Connecting to MongoDB...")
          await dbConnect()

          console.log("Looking for user with email:", credentials.email)
          const user = await User.findOne({ email: credentials.email })

          if (!user) {
            console.log("User not found in database")
            // Production'da daha detaylı log
            if (process.env.NODE_ENV === "production") {
              console.log("Available users count:", await User.countDocuments())
              console.log("Searching for email:", credentials.email)
            }
            return null
          }

          console.log("User found, checking password...")
          const isPasswordValid = await user.comparePassword(credentials.password)

          if (!isPasswordValid) {
            console.log("Invalid password for user:", credentials.email)
            return null
          }

          console.log("Login successful for:", user.email)
          return {
            id: user._id.toString(),
            name: user.name,
            email: user.email,
            role: user.role,
          }
        } catch (error) {
          console.error("Authentication error:", error)
          return null
        }
      },
    }),
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user }) {
      console.log("JWT callback - user:", user ? "exists" : "null")
      console.log("JWT callback - token:", token ? "exists" : "null")
      if (user) {
        token.id = user.id
        token.role = user.role
        console.log("JWT callback - setting token role:", user.role)
      }
      return token
    },
    async session({ session, token }) {
      console.log("Session callback - token:", token ? "exists" : "null")
      if (token) {
        session.user.id = token.id
        session.user.role = token.role
        console.log("Session callback - setting session role:", token.role)
      }
      return session
    },
  },
  pages: {
    signIn: "/admin/login",
    error: "/admin/login",
  },
  secret: process.env.NEXTAUTH_SECRET || "fallback-secret-do-not-use-in-production",

  // Production ortamında debug
  debug: true, // Geçici olarak production'da da debug açık

  // Cookies ayarları
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === "production" ? `__Secure-next-auth.session-token` : `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === "production",
        domain: process.env.NODE_ENV === "production" ? ".ucusatolyesi.com.tr" : undefined
      }
    },
    callbackUrl: {
      name: process.env.NODE_ENV === "production" ? `__Secure-next-auth.callback-url` : `next-auth.callback-url`,
      options: {
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === "production",
        domain: process.env.NODE_ENV === "production" ? ".ucusatolyesi.com.tr" : undefined
      }
    },
    csrfToken: {
      name: process.env.NODE_ENV === "production" ? `__Host-next-auth.csrf-token` : `next-auth.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === "production"
      }
    }
  },
}
