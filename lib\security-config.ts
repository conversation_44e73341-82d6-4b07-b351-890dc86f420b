// Güvenlik konfigürasyonu

export const SecurityConfig = {
  // API Docs erişim kontrolü
  API_DOCS_ENABLED: process.env.ENABLE_API_DOCS === 'true',
  
  // Development modunda API docs erişimi
  DEV_API_DOCS_ENABLED: process.env.NODE_ENV === 'development',
  
  // Production'da API docs için zorunlu admin auth
  REQUIRE_ADMIN_FOR_API_DOCS: process.env.NODE_ENV === 'production',
  
  // Güvenlik logları
  SECURITY_LOGGING: process.env.ENABLE_SECURITY_LOGS === 'true' || process.env.NODE_ENV === 'development',
  
  // Rate limiting
  RATE_LIMIT_ENABLED: process.env.ENABLE_RATE_LIMIT === 'true',
  
  // IP whitelist (production için)
  ADMIN_IP_WHITELIST: process.env.ADMIN_IP_WHITELIST?.split(',') || [],
}

// Güvenlik logları
export function securityLog(message: string, data?: any) {
  if (SecurityConfig.SECURITY_LOGGING) {
    console.log(`[SECURITY] ${message}`, data || '')
  }
}

// API Docs erişim kontrolü
export function isApiDocsAccessAllowed(): boolean {
  // Development modunda her zaman izin ver
  if (SecurityConfig.DEV_API_DOCS_ENABLED) {
    return true
  }
  
  // Production'da environment variable kontrolü
  if (SecurityConfig.API_DOCS_ENABLED) {
    return true
  }
  
  return false
}

// IP whitelist kontrolü
export function isIpWhitelisted(ip: string): boolean {
  if (SecurityConfig.ADMIN_IP_WHITELIST.length === 0) {
    return true // Whitelist boşsa herkese izin ver
  }
  
  return SecurityConfig.ADMIN_IP_WHITELIST.includes(ip)
}

// Request güvenlik kontrolü
export function performSecurityCheck(request: any): { allowed: boolean; reason?: string } {
  // IP kontrolü
  const clientIp = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
  
  if (!isIpWhitelisted(clientIp)) {
    securityLog('IP whitelist check failed', { ip: clientIp })
    return { allowed: false, reason: 'IP not whitelisted' }
  }
  
  // API Docs erişim kontrolü
  if (!isApiDocsAccessAllowed()) {
    securityLog('API Docs access disabled', { ip: clientIp })
    return { allowed: false, reason: 'API Docs disabled' }
  }
  
  return { allowed: true }
}
