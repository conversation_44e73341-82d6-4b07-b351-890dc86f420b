import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import SiteSettings from "@/lib/models/site-settings"
import { handleApiError } from "@/lib/api-utils"
import { checkAdminAuth } from "@/lib/auth-utils"

export async function GET(request: Request) {
  try {
    // Admin auth kontrolü
    const authCheck = await checkAdminAuth()
    if (!authCheck.authorized) {
      return NextResponse.json({ success: false, error: authCheck.error }, { status: 401 })
    }

    await dbConnect()

    let settings = await SiteSettings.findOne({ isDefault: true })

    // Eğer ayarlar yoksa varsayılan ayarları oluştur
    if (!settings) {
      settings = await SiteSettings.create({
        isDefault: true,
        siteName: "Uçuş Atölyesi",
        siteDescription: "Çocuklar için STEM tabanlı uçuş ve teknoloji atölyesi",
        siteLogo: "/logo.jpg",
        contact: {
          phone: "+90 (212) 123 45 67",
          email: "<EMAIL>",
          address: "İstanbul, Türkiye",
          workingHours: "Pazartesi - Cuma: 09:00 - 18:00",
        },
        socialMedia: {
          facebook: "https://facebook.com/ucusatolyesi",
          instagram: "https://instagram.com/ucusatolyesi",
          twitter: "https://twitter.com/ucusatolyesi",
          youtube: "https://youtube.com/@ucusatolyesi",
        },
        footer: {
          aboutText: "Uçuş Atölyesi, çocuklara drone teknolojisi ve havacılık bilimini eğlenceli ve eğitici bir ortamda öğreten bir STEM eğitim merkezidir.",
          quickLinks: [
            { title: "Ana Sayfa", url: "/", isActive: true },
            { title: "Hakkımızda", url: "/about", isActive: true },
            { title: "Eğitimler", url: "/programs", isActive: true },
            { title: "Galeri", url: "/gallery", isActive: true },
            { title: "Blog", url: "/blog", isActive: true },
            { title: "İletişim", url: "/contact", isActive: true },
          ],
          services: [
            { title: "UA1 - Giriş Seviyesi", url: "/programs/ua1", isActive: true },
            { title: "UA2 - Orta Seviye", url: "/programs/ua2", isActive: true },
            { title: "UA3 - İleri Seviye", url: "/programs/ua3", isActive: true },
            { title: "Özel Eğitimler", url: "/programs", isActive: true },
          ],
          copyrightText: "© 2024 Uçuş Atölyesi. Tüm hakları saklıdır.",
        },
      })
    }

    return NextResponse.json({ success: true, data: settings })
  } catch (error) {
    console.error("Error fetching site settings:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function PUT(request: Request) {
  try {
    // Admin auth kontrolü
    const authCheck = await checkAdminAuth()
    if (!authCheck.authorized) {
      return NextResponse.json({ success: false, error: authCheck.error }, { status: 401 })
    }

    await dbConnect()

    const body = await request.json()

    let settings = await SiteSettings.findOne({ isDefault: true })

    if (!settings) {
      // Eğer ayarlar yoksa yeni oluştur
      settings = await SiteSettings.create({
        ...body,
        isDefault: true,
      })
    } else {
      // Mevcut ayarları güncelle
      Object.assign(settings, body)
      await settings.save()
    }

    return NextResponse.json({ success: true, data: settings })
  } catch (error) {
    console.error("Error updating site settings:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
