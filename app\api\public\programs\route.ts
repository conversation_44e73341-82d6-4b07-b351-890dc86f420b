import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Program from "@/lib/models/program"

/**
 * @swagger
 * /api/public/programs:
 *   get:
 *     summary: Tüm aktif eğitim programlarını listele
 *     description: <PERSON><PERSON><PERSON> açık, aktif durumda olan tüm eğitim programlarını getirir
 *     tags: [Public, Programs]
 *     responses:
 *       200:
 *         description: Programlar başarıyla getirildi
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Program'
 *       500:
 *         description: Sunucu hatası
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function GET() {
  try {
    await dbConnect()

    const programs = await Program.find({ isActive: true })
      .select('title slug description age duration price image icon features')
      .sort({ createdAt: -1 })

    return NextResponse.json({ success: true, data: programs })
  } catch (error) {
    console.error("Error fetching public programs:", error)
    return NextResponse.json({ success: false, error: "Programlar yüklenirken hata oluştu" }, { status: 500 })
  }
}
