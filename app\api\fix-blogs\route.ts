import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Blog from "@/lib/models/blog"

export async function GET() {
  try {
    console.log("=== FIX BLOGS PUBLISHED DATE ===")
    
    await dbConnect()
    console.log("Database connected")
    
    // publishedAt alanı olmayan veya null olan yayınlanmış blogları bul
    const blogsToFix = await Blog.find({
      isPublished: true,
      $or: [
        { publishedAt: { $exists: false } },
        { publishedAt: null }
      ]
    })
    
    console.log("Blogs to fix:", blogsToFix.length)
    
    let fixedCount = 0
    
    for (const blog of blogsToFix) {
      // createdAt tarihini publishedAt olarak kullan
      blog.publishedAt = blog.createdAt || new Date()
      await blog.save()
      fixedCount++
      console.log(`Fixed blog: ${blog.title} - ${blog.publishedAt}`)
    }
    
    // Tüm blogları kontrol et
    const allBlogs = await Blog.find({}).select('title slug publishedAt isPublished createdAt')
    
    return NextResponse.json({
      success: true,
      message: `${fixedCount} blog fixed`,
      debug: {
        totalBlogs: allBlogs.length,
        fixedBlogs: fixedCount,
        blogs: allBlogs.map(blog => ({
          title: blog.title,
          slug: blog.slug,
          isPublished: blog.isPublished,
          publishedAt: blog.publishedAt,
          createdAt: blog.createdAt,
          hasValidPublishedAt: !!blog.publishedAt && !isNaN(new Date(blog.publishedAt).getTime())
        }))
      }
    })
    
  } catch (error) {
    console.error("Fix blogs error:", error)
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 })
  }
}
