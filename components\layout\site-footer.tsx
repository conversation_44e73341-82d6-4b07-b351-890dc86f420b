"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { MapPin, Phone, Mail, Facebook, Instagram, Twitter, Youtube, Linkedin } from "lucide-react"
import { ImageWithFallback } from "@/components/image-with-fallback"

export function SiteFooter() {
  const [siteSettings, setSiteSettings] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchSiteSettings = async () => {
      try {
        const response = await fetch('/api/public/site-settings')
        const data = await response.json()

        if (data.success) {
          console.log("Footer: Site settings loaded:", data.data)
          console.log("Footer: Logo URL:", data.data.siteLogo)
          setSiteSettings(data.data)
        }
      } catch (error) {
        console.error('Error fetching site settings:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchSiteSettings()
  }, [])

  // Fallback data
  const fallbackSettings = {
    siteName: "Uçuş Atölyesi",
    siteLogo: "",
    contact: {
      phone: "+90 212 123 45 67",
      email: "<EMAIL>",
      address: "Şişli, İstanbul",
      workingHours: "Pazartesi - Cuma: 09:00 - 18:00",
    },
    socialMedia: {
      facebook: "https://facebook.com/ucusatolyesi",
      instagram: "https://instagram.com/ucusatolyesi",
      twitter: "https://twitter.com/ucusatolyesi",
      youtube: "https://youtube.com/@ucusatolyesi",
    },
    footer: {
      aboutText: "Çocuklar için STEM tabanlı drone ve havacılık eğitim merkezi.",
      quickLinks: [
        { title: "Hakkımızda", url: "/about", isActive: true },
        { title: "Eğitimler", url: "/programs", isActive: true },
        { title: "Eğitmenler", url: "/instructors", isActive: true },
        { title: "Galeri", url: "/gallery", isActive: true },
        { title: "Blog", url: "/blog", isActive: true },
        { title: "İletişim", url: "/contact", isActive: true },
      ],
      services: [
        { title: "UA1 - Giriş Seviyesi", url: "/programs/ua1", isActive: true },
        { title: "UA2 - Orta Seviye", url: "/programs/ua2", isActive: true },
        { title: "UA3 - İleri Seviye", url: "/programs/ua3", isActive: true },
      ],
      copyrightText: "© 2024 Uçuş Atölyesi. Tüm hakları saklıdır.",
    },
  }

  const settings = siteSettings || fallbackSettings

  console.log("Footer: Current settings:", settings)
  console.log("Footer: Logo to display:", settings.siteLogo)

  const getSocialIcon = (platform) => {
    switch (platform) {
      case 'facebook':
        return <Facebook className="h-5 w-5" />
      case 'instagram':
        return <Instagram className="h-5 w-5" />
      case 'twitter':
        return <Twitter className="h-5 w-5" />
      case 'youtube':
        return <Youtube className="h-5 w-5" />
      case 'linkedin':
        return <Linkedin className="h-5 w-5" />
      default:
        return null
    }
  }
  return (
    <footer className="bg-sky-900 dark:bg-gray-900 text-white py-12">
      <div className="container">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="space-y-4">
            <Link href="/" className="flex items-center gap-2">
              {settings.siteLogo ? (
                <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-sky-300 shadow-sm bg-white">
                  <img
                    src={settings.siteLogo}
                    alt={`${settings.siteName} Logo`}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      console.log("Footer: Logo yüklenemedi:", settings.siteLogo)
                      e.target.style.display = 'none'
                    }}
                    onLoad={() => {
                      console.log("Footer: Logo başarıyla yüklendi:", settings.siteLogo)
                    }}
                  />
                </div>
              ) : (
                <div className="w-10 h-10 bg-sky-200 rounded-full border-2 border-sky-300 shadow-sm flex items-center justify-center">
                  <span className="text-sky-800 font-bold text-lg">🚁</span>
                </div>
              )}
              <span className="text-xl font-bold">{settings.siteName}</span>
            </Link>
            <p className="text-sky-200 dark:text-gray-300 text-sm">
              {settings.footer.aboutText}
            </p>
          </div>
          <div>
            <h3 className="font-semibold text-lg mb-4">Hızlı Erişim</h3>
            <ul className="space-y-2">
              {settings.footer.quickLinks
                .filter(link => link.isActive)
                .map((link, index) => (
                  <li key={index}>
                    <Link
                      href={link.url}
                      className="text-sky-200 dark:text-gray-300 hover:text-white transition-colors"
                    >
                      {link.title}
                    </Link>
                  </li>
                ))}
            </ul>
          </div>
          <div>
            <h3 className="font-semibold text-lg mb-4">İletişim</h3>
            <ul className="space-y-2">
              {settings.contact.address && (
                <li className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  <span className="text-sky-200 dark:text-gray-300">{settings.contact.address}</span>
                </li>
              )}
              {settings.contact.phone && (
                <li className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  <span className="text-sky-200 dark:text-gray-300">{settings.contact.phone}</span>
                </li>
              )}
              {settings.contact.email && (
                <li className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  <span className="text-sky-200 dark:text-gray-300">{settings.contact.email}</span>
                </li>
              )}
              {settings.contact.workingHours && (
                <li className="text-sky-200 dark:text-gray-300 text-sm">
                  {settings.contact.workingHours}
                </li>
              )}
            </ul>
          </div>
          <div>
            <h3 className="font-semibold text-lg mb-4">Bizi Takip Edin</h3>
            <div className="flex gap-4">
              {Object.entries(settings.socialMedia)
                .filter(([platform, url]) => url && url.trim() !== '')
                .map(([platform, url]) => (
                  <a
                    key={platform}
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sky-200 dark:text-gray-300 hover:text-white transition-colors"
                    title={platform.charAt(0).toUpperCase() + platform.slice(1)}
                  >
                    {getSocialIcon(platform)}
                  </a>
                ))}
            </div>

            {/* Hizmetler Bölümü */}
            {settings.footer.services && settings.footer.services.length > 0 && (
              <div className="mt-6">
                <h4 className="font-semibold text-base mb-3">Hizmetlerimiz</h4>
                <ul className="space-y-1">
                  {settings.footer.services
                    .filter(service => service.isActive)
                    .map((service, index) => (
                      <li key={index}>
                        <Link
                          href={service.url}
                          className="text-sky-200 dark:text-gray-300 hover:text-white transition-colors text-sm"
                        >
                          {service.title}
                        </Link>
                      </li>
                    ))}
                </ul>
              </div>
            )}
          </div>
        </div>
        <div className="border-t border-sky-800 dark:border-gray-700 mt-8 pt-8 text-center text-sm text-sky-300 dark:text-gray-400">
          <p>{settings.footer.copyrightText}</p>
        </div>
      </div>
    </footer>
  )
}
