import mongoose from "mongoose"
import { mockDbConnect } from "./mock-db"

// Yerel MongoDB bağlantı URI'si
const MONGODB_URI = process.env.MONGODB_URI || "mongodb://localhost:27017/ucus-atolyesi"

/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */
let cached = global.mongoose

if (!cached) {
  cached = global.mongoose = { conn: null, promise: null }
}

async function dbConnect() {
  try {
    if (cached.conn) {
      return cached.conn
    }

    if (!cached.promise) {
      const opts = {
        bufferCommands: false,
      }

      cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {
        console.log("MongoDB bağlantısı başarılı!")
        return mongoose
      })
    }
    cached.conn = await cached.promise
    return cached.conn
  } catch (error) {
    console.error("MongoDB bağlantı hatası:", error)
    // Bağlantı hatası durumunda mock veritabanını kullan
    console.log("Mock veritabanı kullanılıyor...")
    return await mockDbConnect()
  }
}

export default dbConnect
