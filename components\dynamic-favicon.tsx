"use client"

import { useEffect, useState } from "react"

export function DynamicFavicon() {
  const [siteSettings, setSiteSettings] = useState(null)

  useEffect(() => {
    const fetchSiteSettings = async () => {
      try {
        const response = await fetch('/api/public/site-settings')
        const data = await response.json()

        if (data.success) {
          setSiteSettings(data.data)
        }
      } catch (error) {
        console.error('Error fetching site settings for favicon:', error)
      }
    }

    fetchSiteSettings()
  }, [])

  useEffect(() => {
    if (siteSettings) {
      updateFavicon(siteSettings.siteLogo, siteSettings.siteName)
      updatePageTitle(siteSettings.siteName)
    }
  }, [siteSettings])

  const updateFavicon = (logoUrl: string, siteName: string) => {
    // Mevcut favicon linklerini temizle (sadece dinamik olanları)
    const existingDynamicFavicons = document.querySelectorAll('link[data-dynamic="true"]')
    existingDynamicFavicons.forEach(link => link.remove())

    if (logoUrl) {
      // Logo varsa onu daire çerçeve içinde favicon olarak kullan
      createLogoFavicon(logoUrl)
    } else {
      // Logo yoksa emoji favicon oluştur
      createEmojiFavicon()
    }
  }

  const createLogoFavicon = (logoUrl: string) => {
    // Canvas ile logo favicon oluştur
    const canvas = document.createElement('canvas')
    canvas.width = 32
    canvas.height = 32
    const ctx = canvas.getContext('2d')

    if (ctx) {
      const img = new Image()
      img.crossOrigin = 'anonymous'

      img.onload = () => {
        // Dış çerçeve (border)
        ctx.fillStyle = '#0284c7' // sky-600 (daha koyu çerçeve)
        ctx.beginPath()
        ctx.arc(16, 16, 16, 0, 2 * Math.PI)
        ctx.fill()

        // İç daire arka plan (beyaz)
        ctx.fillStyle = '#ffffff'
        ctx.beginPath()
        ctx.arc(16, 16, 13, 0, 2 * Math.PI) // 3px çerçeve için 13px radius
        ctx.fill()

        // Logo'yu daire içine çiz (clipping)
        ctx.save()
        ctx.beginPath()
        ctx.arc(16, 16, 12, 0, 2 * Math.PI) // Logo için 12px radius
        ctx.clip()

        // Logo'yu çiz
        const logoSize = 24 // 12px radius * 2
        ctx.drawImage(img, 4, 4, logoSize, logoSize)
        ctx.restore()

        // Canvas'ı favicon olarak ayarla
        const link = document.createElement('link')
        link.rel = 'icon'
        link.type = 'image/png'
        link.href = canvas.toDataURL()
        link.setAttribute('data-dynamic', 'true')
        document.head.appendChild(link)

        // Apple touch icon için de ekle
        const appleTouchIcon = document.createElement('link')
        appleTouchIcon.rel = 'apple-touch-icon'
        appleTouchIcon.href = canvas.toDataURL()
        appleTouchIcon.setAttribute('data-dynamic', 'true')
        document.head.appendChild(appleTouchIcon)

        console.log("Favicon: Daire çerçeveli logo favicon oluşturuldu:", logoUrl)
      }

      img.onerror = () => {
        console.log("Favicon: Logo yüklenemedi, emoji favicon oluşturuluyor")
        createEmojiFavicon()
      }

      img.src = logoUrl
    }
  }

  const createEmojiFavicon = () => {
    // Canvas ile emoji favicon oluştur
    const canvas = document.createElement('canvas')
    canvas.width = 32
    canvas.height = 32
    const ctx = canvas.getContext('2d')

    if (ctx) {
      // Dış çerçeve (border)
      ctx.fillStyle = '#0284c7' // sky-600 (daha koyu çerçeve)
      ctx.beginPath()
      ctx.arc(16, 16, 16, 0, 2 * Math.PI)
      ctx.fill()

      // İç daire arka plan
      ctx.fillStyle = '#0ea5e9' // sky-500
      ctx.beginPath()
      ctx.arc(16, 16, 13, 0, 2 * Math.PI) // 3px çerçeve için 13px radius
      ctx.fill()

      // Emoji
      ctx.font = '16px Arial'
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      ctx.fillStyle = '#ffffff'
      ctx.fillText('🚁', 16, 16)

      // Canvas'ı favicon olarak ayarla
      const link = document.createElement('link')
      link.rel = 'icon'
      link.type = 'image/png'
      link.href = canvas.toDataURL()
      link.setAttribute('data-dynamic', 'true')
      document.head.appendChild(link)

      console.log("Favicon: Daire çerçeveli emoji favicon oluşturuldu")
    }
  }

  const updatePageTitle = (siteName: string) => {
    if (siteName && document.title.includes("Uçuş Atölyesi")) {
      // Sadece ana sayfa başlığını güncelle, diğer sayfalara dokunma
      if (document.title === "Uçuş Atölyesi - Drone ve Havacılık Eğitim Merkezi") {
        document.title = `${siteName} - Drone ve Havacılık Eğitim Merkezi`
      }
    }
  }

  return null // Bu component görsel bir şey render etmez
}
