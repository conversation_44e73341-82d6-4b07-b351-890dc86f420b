"use client"

import { useSession } from "next-auth/react"
import { useRouter, usePathname } from "next/navigation"
import { useEffect } from "react"
import { AdminSidebar } from "@/components/admin/admin-sidebar"
import { AdminFavicon } from "@/components/admin/admin-favicon"
import { debugAuthIssues } from "@/lib/auth-debug"

export default function AdminLayout({ children }) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // Geliştirme modunda her zaman admin layout'u göster
    if (process.env.NODE_ENV === "development") {
      return
    }

    // Login sayfasında değilsek ve session yoksa login'e yönlendir
    if (status === "loading") return // Yükleniyor

    if (!session && pathname !== "/admin/login") {
      router.push("/admin/login")
      return
    }

    // Session varsa ama admin değilse ana sayfaya yönlendir
    if (session && session.user?.role !== "admin" && pathname !== "/admin/login") {
      router.push("/")
      return
    }
  }, [session, status, router, pathname])

  // Geliştirme modunda her zaman göster
  if (process.env.NODE_ENV === "development") {
    // Auth debug bilgilerini göster
    useEffect(() => {
      debugAuthIssues()
    }, [])

    return (
      <>
        <AdminFavicon />
        <div className="flex flex-col lg:flex-row h-screen bg-gray-50">
          <AdminSidebar user={{ name: "Geliştirici", email: "<EMAIL>", role: "admin" }} />
          <main className="flex-1 overflow-auto">
            <div className="p-4 lg:p-6">
              {children}
            </div>
          </main>
        </div>
      </>
    )
  }

  // Login sayfasında layout gösterme
  if (pathname === "/admin/login") {
    return <>{children}</>
  }

  // Yükleniyor durumu
  if (status === "loading") {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-sky-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Yükleniyor...</p>
        </div>
      </div>
    )
  }

  // Session yoksa veya admin değilse hiçbir şey gösterme
  if (!session || session.user?.role !== "admin") {
    return null
  }

  return (
    <>
      <AdminFavicon />
      <div className="flex flex-col lg:flex-row h-screen bg-gray-50">
        <AdminSidebar user={session.user} />
        <main className="flex-1 overflow-auto">
          <div className="p-4 lg:p-6">
            {children}
          </div>
        </main>
      </div>
    </>
  )
}
