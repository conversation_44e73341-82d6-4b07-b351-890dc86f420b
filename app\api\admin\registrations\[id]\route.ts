import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Registration from "@/lib/models/registration"
import { handleApiError } from "@/lib/api-utils"

export async function GET(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    await dbConnect()

    const { id } = await params
    const registration = await Registration.findById(id)

    if (!registration) {
      return NextResponse.json({ success: false, error: "Kayıt bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: registration })
  } catch (error) {
    console.error("Error fetching registration:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const body = await request.json()

    await dbConnect()

    const registration = await Registration.findByIdAndUpdate(params.id, body, {
      new: true,
      runValidators: true,
    })

    if (!registration) {
      return NextResponse.json({ success: false, error: "Kayıt bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: registration })
  } catch (error) {
    console.error("Error updating registration:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    await dbConnect()

    const registration = await Registration.findByIdAndDelete(params.id)

    if (!registration) {
      return NextResponse.json({ success: false, error: "Kayıt bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, message: "Kayıt başarıyla silindi" })
  } catch (error) {
    console.error("Error deleting registration:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
