"use client"
import Link from "next/link"
import { <PERSON><PERSON><PERSON>, ChevronDown } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { motion } from "framer-motion"
import { FAQSection } from "@/components/faq-section"
// Import the EnrollmentForm component at the top of the file
import { EnrollmentForm } from "@/components/enrollment-form"
import { TestimonialForm } from "@/components/testimonial-form"
import { ImageWithFallback } from "@/components/image-with-fallback"
import { useEffect, useState } from "react"

export default function Home() {
  const [programs, setPrograms] = useState([])
  const [testimonials, setTestimonials] = useState([])
  const [gallery, setGallery] = useState([])
  const [blogs, setBlogs] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [programsRes, testimonialsRes, galleryRes, blogsRes] = await Promise.all([
          fetch('/api/public/programs'),
          fetch('/api/public/testimonials'),
          fetch('/api/public/gallery'),
          fetch('/api/public/blogs?limit=3')
        ])

        const [programsData, testimonialsData, galleryData, blogsData] = await Promise.all([
          programsRes.json(),
          testimonialsRes.json(),
          galleryRes.json(),
          blogsRes.json()
        ])

        if (programsData.success) setPrograms(programsData.data.slice(0, 3))
        if (testimonialsData.success) setTestimonials(testimonialsData.data.slice(0, 3))
        if (galleryData.success) setGallery(galleryData.data.slice(0, 6))
        if (blogsData.success) setBlogs(blogsData.data.slice(0, 3))
      } catch (error) {
        console.error('Error fetching data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])
  return (
    <div className="flex min-h-screen flex-col">
      {/* Hero Section */}
      <section className="relative h-[90vh] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <ImageWithFallback
            src="/drone-5.jpg"
            alt="Drone flying over clouds"
            fill
            priority
            className="object-cover"
            type="hero"
          />
          {/* <div className="absolute inset-0 bg-gradient-to-r from-sky-900/70 to-sky-700/50 dark:from-gray-900/80 dark:to-sky-900/70" /> */}
        </div>
        <div className="container relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl text-white"
          >
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">Drone Teknolojisine İlk Adım!</h1>
            <p className="mt-6 text-xl text-white/90">Çocuklar için STEM tabanlı uçuş ve teknoloji atölyesi</p>
            <div className="mt-10 flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="bg-sky-500 hover:bg-sky-600" asChild>
                <Link href="/about">
                  Keşfet
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button style={{}} size="lg"  variant="outline" className="bg-red-500 hover:bg-red-600 text-white" asChild>
                <Link href="/programs">Eğitimler</Link>
              </Button>
            </div>
          </motion.div>
        </div>
        <div className="absolute bottom-8 left-1/2 -translate-x-1/2 animate-bounce">
          <Link href="#about">
            <ChevronDown className="h-8 w-8 text-white" />
          </Link>
        </div>
      </section>
     

      {/* About Section */}
      <section id="about" className="py-24 bg-white dark:bg-gray-800">
        <div className="container">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative h-[400px] rounded-lg overflow-hidden"
            >
              <ImageWithFallback
                src="/about-image-1.jpg"
                alt="Children learning about drones"
                fill
                className="object-cover"
                type="about"
              />
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
                Biz Kimiz?
              </h2>
              <div className="mt-6 space-y-6 text-lg text-gray-600 dark:text-gray-300">
                <p>
                  Uçuş Atölyesi, çocuklara drone teknolojisi ve havacılık bilimini eğlenceli ve eğitici bir ortamda
                  öğreten bir STEM eğitim merkezidir.
                </p>
                <p>
                  6-14 yaş arası çocuklar için tasarlanmış programlarımızla, geleceğin teknoloji liderlerini
                  yetiştirmeyi hedefliyoruz.
                </p>
              </div>
              <div className="mt-8">
                <Button className="bg-sky-500 hover:bg-sky-600" asChild>
                  <Link href="/about">Daha Fazla Bilgi</Link>
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Programs Section */}
      <section
        id="programs"
        className="py-24 bg-gradient-to-b from-sky-50 to-white dark:from-gray-900 dark:to-gray-800"
      >
        <div className="container">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
              Eğitim Programlarımız
            </h2>
            <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
              Farklı yaş grupları ve beceri seviyelerine göre tasarlanmış programlarımızla çocukların drone
              teknolojisini keşfetmelerini sağlıyoruz.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {loading ? (
              // Loading skeleton
              [...Array(3)].map((_, index) => (
                <div key={index} className="h-80 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
              ))
            ) : programs.length > 0 ? (
              programs.map((program, index) => (
                <motion.div
                  key={program._id || index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -10 }}
                  className="h-full"
                >
                  <Card className="h-full border-2 border-sky-100 dark:border-sky-900 hover:border-sky-300 dark:hover:border-sky-700 transition-colors">
                    <CardHeader>
                      <div className="text-4xl mb-4">{program.icon || "🚁"}</div>
                      <CardTitle>{program.title}</CardTitle>
                      <CardDescription className="dark:text-gray-400">Yaş Grubu: {program.age}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="dark:text-gray-300">{program.description}</p>
                    </CardContent>
                    <CardFooter>
                      <Button variant="outline" className="w-full" asChild>
                        <Link href={`/programs/${program.slug}`}>Detayları Gör</Link>
                      </Button>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))
            ) : (
              // Fallback static data
              [
                {
                  title: "UA1 - Giriş Seviyesi",
                  description: "Drone teknolojisine ilk adım. Temel uçuş prensipleri, güvenlik kuralları ve basit kontroller.",
                  age: "6-8 yaş",
                  icon: "🚁",
                  slug: "ua1",
                },
                {
                  title: "UA2 - Orta Seviye",
                  description: "Daha karmaşık uçuş teknikleri, basit kodlama ve drone parçalarının tanıtımı.",
                  age: "9-11 yaş",
                  icon: "⚙️",
                  slug: "ua2",
                },
                {
                  title: "UA3 - İleri Seviye",
                  description: "Drone programlama, 3D tasarım, yarışma hazırlıkları ve proje geliştirme.",
                  age: "12-14 yaş",
                  icon: "💻",
                  slug: "ua3",
                },
              ].map((program, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -10 }}
                  className="h-full"
                >
                  <Card className="h-full border-2 border-sky-100 dark:border-sky-900 hover:border-sky-300 dark:hover:border-sky-700 transition-colors">
                    <CardHeader>
                      <div className="text-4xl mb-4">{program.icon}</div>
                      <CardTitle>{program.title}</CardTitle>
                      <CardDescription className="dark:text-gray-400">Yaş Grubu: {program.age}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="dark:text-gray-300">{program.description}</p>
                    </CardContent>
                    <CardFooter>
                      <Button variant="outline" className="w-full" asChild>
                        <Link href={`/programs/${program.slug}`}>Detayları Gör</Link>
                      </Button>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))
            )}
          </div>
          <div className="text-center mt-12">
            <Button size="lg" className="bg-sky-500 hover:bg-sky-600" asChild>
              <Link href="/programs">Tüm Programları Gör</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Gallery Preview */}
      <section id="gallery" className="py-24 bg-sky-50 dark:bg-gray-900">
        <div className="container">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
              Atölye Çalışmalarımız
            </h2>
            <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
              Öğrencilerimizin eğitim sürecinde yaşadıkları deneyimlerden kareler.
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {loading ? (
              [...Array(6)].map((_, index) => (
                <div key={index} className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
              ))
            ) : gallery.length > 0 ? (
              gallery.map((item, index) => (
                <motion.div
                  key={item._id || index}
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05 }}
                  className="relative h-64 overflow-hidden rounded-lg cursor-pointer"
                  onClick={() => window.open('/gallery', '_blank')}
                >
                  <ImageWithFallback
                    src={item.type === "video" ? item.videoThumbnail : item.image || `/gallery-${index + 1}.jpg`}
                    alt={item.title || `Gallery image ${index + 1}`}
                    fill
                    className="object-cover"
                    type="gallery"
                  />
                  {/* Video Play Button */}
                  {item.type === "video" && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="bg-red-600 rounded-full p-3 opacity-90">
                        <svg className="h-6 w-6 text-white fill-white" viewBox="0 0 24 24">
                          <path d="M8 5v14l11-7z"/>
                        </svg>
                      </div>
                    </div>
                  )}
                  {/* Video Badge */}
                  {item.type === "video" && (
                    <div className="absolute top-2 left-2">
                      <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium">
                        VIDEO
                      </span>
                    </div>
                  )}
                  {item.title && (
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                      <p className="text-white text-sm font-medium">
                        {item.title || (item.type === "video" ? item.videoTitle : "")}
                      </p>
                    </div>
                  )}
                </motion.div>
              ))
            ) : (
              [...Array(6)].map((_, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05 }}
                  className="relative h-64 overflow-hidden rounded-lg"
                >
                  <ImageWithFallback
                    src={`/gallery-${index + 1}.jpg`}
                    alt={`Gallery image ${index + 1}`}
                    fill
                    className="object-cover"
                    type="gallery"
                  />
                </motion.div>
              ))
            )}
          </div>
          <div className="text-center mt-12">
            <Button size="lg" className="bg-sky-500 hover:bg-sky-600" asChild>
              <Link href="/gallery">Galeriyi Gör</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Testimonials Preview */}
      <section id="testimonials" className="py-24 bg-white dark:bg-gray-800">
        <div className="container">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
              Velilerimizin Yorumları
            </h2>
            <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
              Çocuklarımızın eğitim sürecinde velilerimizin deneyimleri ve geri bildirimleri.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {loading ? (
              [...Array(3)].map((_, index) => (
                <div key={index} className="h-48 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
              ))
            ) : testimonials.length > 0 ? (
              testimonials.map((testimonial, index) => (
                <motion.div
                  key={testimonial._id || index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className="h-full dark:border-gray-700">
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="relative h-12 w-12 overflow-hidden rounded-full">
                          <ImageWithFallback
                            src={testimonial.image || "/placeholder.svg"}
                            alt={testimonial.name}
                            fill
                            className="object-cover"
                            type="testimonial"
                          />
                        </div>
                        <div>
                          <h3 className="font-semibold dark:text-white">{testimonial.name}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">{testimonial.role}</p>
                        </div>
                      </div>
                      <p className="italic text-gray-600 dark:text-gray-300">"{testimonial.quote}"</p>
                      {testimonial.rating && (
                        <div className="flex mt-3">
                          {[...Array(5)].map((_, i) => (
                            <span key={i} className={`text-lg ${i < testimonial.rating ? 'text-yellow-400' : 'text-gray-300'}`}>
                              ★
                            </span>
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              ))
            ) : (
              // Fallback static data
              [
                {
                  name: "Selin Yıldız",
                  role: "Mert'in annesi",
                  quote: "Oğlum Mert'in teknolojiye olan ilgisi Uçuş Atölyesi ile bambaşka bir boyut kazandı. Artık evde sürekli yeni projeler geliştiriyor.",
                  image: "/testimonial-1.jpg",
                },
                {
                  name: "Kemal Öztürk",
                  role: "Defne'nin babası",
                  quote: "Kızım Defne'nin özgüveni bu eğitimlerle çok arttı. Drone uçurmayı öğrenmek onu çok mutlu etti ve bilime olan ilgisini artırdı.",
                  image: "/testimonial-2.jpg",
                },
                {
                  name: "Elif Kara",
                  role: "Can'ın annesi",
                  quote: "Eğitmenler çok ilgili ve sabırlı. Can'ın öğrenme sürecinde ona çok destek oldular. Kesinlikle tavsiye ediyorum.",
                  image: "/testimonial-3.jpg",
                },
              ].map((testimonial, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className="h-full dark:border-gray-700">
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="relative h-12 w-12 overflow-hidden rounded-full">
                          <ImageWithFallback
                            src={testimonial.image || "/placeholder.svg"}
                            alt={testimonial.name}
                            fill
                            className="object-cover"
                            type="testimonial"
                          />
                        </div>
                        <div>
                          <h3 className="font-semibold dark:text-white">{testimonial.name}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">{testimonial.role}</p>
                        </div>
                      </div>
                      <p className="italic text-gray-600 dark:text-gray-300">"{testimonial.quote}"</p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))
            )}
          </div>
          <div className="text-center mt-12">
            <Button size="lg" className="bg-sky-500 hover:bg-sky-600" asChild>
              <Link href="/testimonials">Tüm Yorumları Gör</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Blog Section */}
      <section id="blog" className="py-24 bg-gradient-to-b from-sky-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="container">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
              Blog Yazılarımız
            </h2>
            <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
              Drone teknolojisi, eğitim ipuçları ve atölye haberlerimizi takip edin.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {loading ? (
              [...Array(3)].map((_, index) => (
                <div key={index} className="h-96 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
              ))
            ) : blogs.length > 0 ? (
              blogs.map((blog, index) => (
                <motion.div
                  key={blog._id || index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -10 }}
                  className="h-full cursor-pointer"
                  onClick={() => window.open(`/blog/${blog.slug}`, '_blank')}
                >
                  <Card className="h-full border-2 border-sky-100 dark:border-sky-900 hover:border-sky-300 dark:hover:border-sky-700 transition-colors overflow-hidden">
                    {blog.featuredImage && (
                      <div className="relative h-48 overflow-hidden">
                        <ImageWithFallback
                          src={blog.featuredImage}
                          alt={blog.title}
                          fill
                          className="object-cover"
                          type="blog"
                        />
                      </div>
                    )}
                    <CardHeader>
                      <div className="flex items-center gap-2 mb-2">
                        <span className="bg-sky-100 text-sky-800 dark:bg-sky-900 dark:text-sky-200 px-2 py-1 rounded text-xs font-medium">
                          {blog.category}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {blog.readTime} dk okuma
                        </span>
                      </div>
                      <CardTitle className="line-clamp-2">{blog.title}</CardTitle>
                      <CardDescription className="dark:text-gray-400 line-clamp-3">
                        {blog.excerpt}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                        <span>{blog.author}</span>
                        <span>{new Date(blog.publishedAt).toLocaleDateString('tr-TR')}</span>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))
            ) : (
              // Fallback static data
              [
                {
                  title: "Drone Teknolojisinin Geleceği",
                  excerpt: "Drone teknolojisinin gelişimi ve çocukların bu alanda nasıl eğitim alabileceği hakkında...",
                  author: "Uçuş Atölyesi",
                  category: "teknoloji",
                  readTime: 5,
                  publishedAt: new Date(),
                  slug: "drone-teknolojisinin-gelecegi",
                  featuredImage: "/blog-1.jpg",
                },
                {
                  title: "STEM Eğitiminde Drone'ların Rolü",
                  excerpt: "STEM eğitiminde drone'ların nasıl kullanıldığı ve çocukların öğrenme sürecine katkıları...",
                  author: "Uçuş Atölyesi",
                  category: "eğitim",
                  readTime: 7,
                  publishedAt: new Date(),
                  slug: "stem-egitiminde-dronelarin-rolu",
                  featuredImage: "/blog-2.jpg",
                },
                {
                  title: "Atölye Etkinliklerimizden Kareler",
                  excerpt: "Bu hafta gerçekleştirdiğimiz drone uçuş etkinliklerinden öne çıkan anlar ve öğrenci başarıları...",
                  author: "Uçuş Atölyesi",
                  category: "etkinlik",
                  readTime: 3,
                  publishedAt: new Date(),
                  slug: "atolye-etkinliklerimizden-kareler",
                  featuredImage: "/blog-3.jpg",
                },
              ].map((blog, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -10 }}
                  className="h-full cursor-pointer"
                  onClick={() => window.open(`/blog/${blog.slug}`, '_blank')}
                >
                  <Card className="h-full border-2 border-sky-100 dark:border-sky-900 hover:border-sky-300 dark:hover:border-sky-700 transition-colors overflow-hidden">
                    <div className="relative h-48 overflow-hidden">
                      <ImageWithFallback
                        src={blog.featuredImage}
                        alt={blog.title}
                        fill
                        className="object-cover"
                        type="blog"
                      />
                    </div>
                    <CardHeader>
                      <div className="flex items-center gap-2 mb-2">
                        <span className="bg-sky-100 text-sky-800 dark:bg-sky-900 dark:text-sky-200 px-2 py-1 rounded text-xs font-medium">
                          {blog.category}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {blog.readTime} dk okuma
                        </span>
                      </div>
                      <CardTitle className="line-clamp-2">{blog.title}</CardTitle>
                      <CardDescription className="dark:text-gray-400 line-clamp-3">
                        {blog.excerpt}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                        <span>{blog.author}</span>
                        <span>{new Date(blog.publishedAt).toLocaleDateString('tr-TR')}</span>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))
            )}
          </div>
          <div className="text-center mt-12">
            <Button size="lg" className="bg-sky-500 hover:bg-sky-600" asChild>
              <Link href="/blog">Tüm Blog Yazılarını Gör</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <FAQSection />

      {/* Testimonial Form */}
      <section className="py-20 bg-gray-50 dark:bg-gray-900">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
              Deneyiminizi Paylaşın
            </h2>
            <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
              Uçuş Atölyesi hakkındaki görüşlerinizi bizimle paylaşın.
            </p>
          </div>
          <TestimonialForm />
        </div>
      </section>

      {/* CTA Section with Enrollment Form */}
      <section className="py-16 bg-sky-600 dark:bg-sky-800 text-white">
        <div className="container">
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div className="text-center md:text-left">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Eğitimlerimize Katılın</h2>
              <p className="mt-4 text-xl text-white/90">
                Çocuğunuzun geleceğine yatırım yapın. Drone teknolojisi ve havacılık bilimi ile tanıştırın.
              </p>
              <div className="mt-8 hidden md:block">
                <Button size="lg" variant="outline" className="bg-red-500 hover:bg-red-600 text-white" asChild>
                  <Link href="/register">Detaylı Kayıt Formu</Link>
                </Button>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-1">
              <EnrollmentForm />
            </div>
            <div className="md:hidden text-center">
              <Button size="lg" variant="outline" className="text-white border-white hover:bg-white/10" asChild>
                <Link href="/register">Detaylı Kayıt Formu</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
