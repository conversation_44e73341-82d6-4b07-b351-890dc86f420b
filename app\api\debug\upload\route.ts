import { NextResponse } from "next/server"
import { existsSync, readdirSync, statSync } from "fs"
import { join } from "path"

export async function GET() {
  try {
    const uploadDir = join(process.cwd(), 'public', 'uploads')
    
    const debug = {
      uploadDirPath: uploadDir,
      uploadDirExists: existsSync(uploadDir),
      publicDirPath: join(process.cwd(), 'public'),
      publicDirExists: existsSync(join(process.cwd(), 'public')),
      currentWorkingDir: process.cwd(),
      files: [],
      subdirectories: []
    }
    
    // Upload klasörü varsa içeriğini listele
    if (debug.uploadDirExists) {
      try {
        const items = readdirSync(uploadDir)
        
        for (const item of items) {
          const itemPath = join(uploadDir, item)
          const stats = statSync(itemPath)
          
          if (stats.isDirectory()) {
            debug.subdirectories.push({
              name: item,
              path: itemPath,
              files: existsSync(itemPath) ? readdirSync(itemPath).length : 0
            })
          } else {
            debug.files.push({
              name: item,
              size: stats.size,
              modified: stats.mtime
            })
          }
        }
      } catch (error) {
        debug.readError = error.message
      }
    }
    
    // Alt klasörlerin içeriğini de kontrol et
    const subDirs = ['gallery', 'instructor', 'profile', 'blog', 'logo', 'general']
    debug.subDirectoryDetails = {}
    
    for (const subDir of subDirs) {
      const subDirPath = join(uploadDir, subDir)
      debug.subDirectoryDetails[subDir] = {
        exists: existsSync(subDirPath),
        path: subDirPath,
        files: []
      }
      
      if (existsSync(subDirPath)) {
        try {
          const files = readdirSync(subDirPath)
          debug.subDirectoryDetails[subDir].files = files.map(file => {
            const filePath = join(subDirPath, file)
            const stats = statSync(filePath)
            return {
              name: file,
              size: stats.size,
              url: `/uploads/${subDir}/${file}`,
              modified: stats.mtime
            }
          })
        } catch (error) {
          debug.subDirectoryDetails[subDir].error = error.message
        }
      }
    }
    
    return NextResponse.json({
      success: true,
      debug
    })
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 })
  }
}
