import mongoose from "mongoose"

// Define the schema
const programSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Program başlığı gereklidir"],
      trim: true,
    },
    slug: {
      type: String,
      required: [true, "Program slug'ı gereklidir"],
      trim: true,
      unique: true,
    },
    description: {
      type: String,
      required: [true, "Program açıklaması gereklidir"],
      trim: true,
    },
    fullDescription: {
      type: String,
      trim: true,
    },
    age: {
      type: String,
      required: [true, "Yaş grubu gereklidir"],
      trim: true,
    },
    duration: {
      type: String,
      trim: true,
    },
    schedule: {
      type: String,
      trim: true,
    },
    price: {
      type: String,
      trim: true,
    },
    image: {
      type: String,
      trim: true,
    },
    icon: {
      type: String,
      trim: true,
    },
    features: {
      type: [String],
      default: [],
    },
    curriculum: [
      {
        week: {
          type: String,
        },
        title: {
          type: String,
        },
        content: {
          type: String,
        },
      },
    ],
    faqs: [
      {
        question: {
          type: String,
        },
        answer: {
          type: String,
        },
      },
    ],
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  },
)

// Check if the model already exists to prevent overwriting
export default mongoose.models.Program || mongoose.model("Program", programSchema)
