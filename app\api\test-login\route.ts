import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import User from "@/lib/models/user"

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json()
    
    console.log("=== TEST LOGIN ===")
    console.log("Email:", email)
    console.log("Password length:", password?.length)
    
    // Database bağlantısı
    await dbConnect()
    console.log("Database connected successfully")
    
    // Kullanıcıyı bul
    const user = await User.findOne({ email: email })
    console.log("User found:", !!user)
    
    if (!user) {
      // Tüm kullanıcıları listele (debug için)
      const allUsers = await User.find({}, 'email name role').limit(5)
      console.log("Available users:", allUsers)
      
      return NextResponse.json({
        success: false,
        error: "User not found",
        debug: {
          searchedEmail: email,
          availableUsers: allUsers.map(u => u.email),
          totalUsers: await User.countDocuments()
        }
      })
    }
    
    console.log("User details:", {
      name: user.name,
      email: user.email,
      role: user.role,
      isActive: user.isActive,
      hasPassword: !!user.password
    })
    
    // Şifre kontrolü
    const isPasswordValid = await user.comparePassword(password)
    console.log("Password validation result:", isPasswordValid)
    
    if (!isPasswordValid) {
      return NextResponse.json({
        success: false,
        error: "Invalid password",
        debug: {
          userFound: true,
          passwordTested: true,
          passwordValid: false,
          user: {
            email: user.email,
            hasPassword: !!user.password,
            passwordLength: user.password?.length
          }
        }
      })
    }
    
    // Başarılı login
    return NextResponse.json({
      success: true,
      message: "Login successful!",
      user: {
        id: user._id.toString(),
        name: user.name,
        email: user.email,
        role: user.role,
        isActive: user.isActive
      }
    })
    
  } catch (error) {
    console.error("Test login error:", error)
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 })
  }
}
