import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Registration from "@/lib/models/registration"
import { handleApiError } from "@/lib/api-utils"

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get("status")
    const program = searchParams.get("program")
    const search = searchParams.get("search")

    await dbConnect()

    let query: any = {}

    if (status && status !== "all") {
      query.status = status
    }

    if (program && program !== "all") {
      query.program = program
    }

    if (search) {
      query.$or = [
        { childName: { $regex: search, $options: "i" } },
        { parentName: { $regex: search, $options: "i" } },
        { email: { $regex: search, $options: "i" } },
      ]
    }

    const registrations = await Registration.find(query).sort({ createdAt: -1 })

    return NextResponse.json({ success: true, data: registrations })
  } catch (error) {
    console.error("Error fetching registrations:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()

    await dbConnect()

    const registration = await Registration.create(body)

    return NextResponse.json({ success: true, data: registration }, { status: 201 })
  } catch (error) {
    console.error("Error creating registration:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
