"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { toast } from "sonner"
import { ArrowLeft, Heart, MessageCircle, Clock, User, Calendar, Send } from "lucide-react"

export default function BlogDetailPage() {
  const params = useParams()
  const slug = params.slug as string
  const [blog, setBlog] = useState(null)
  const [comments, setComments] = useState([])
  const [loading, setLoading] = useState(true)
  const [commentsLoading, setCommentsLoading] = useState(true)
  const [hasLiked, setHasLiked] = useState(false)
  const [isSubmittingComment, setIsSubmittingComment] = useState(false)
  const [commentForm, setCommentForm] = useState({
    name: "",
    email: "",
    comment: "",
  })

  useEffect(() => {
    const fetchBlog = async () => {
      try {
        const response = await fetch(`/api/public/blogs/${slug}`)
        const data = await response.json()

        if (data.success) {
          setBlog(data.data)
          // Check if user has liked this blog (from localStorage)
          const likedBlogs = JSON.parse(localStorage.getItem('likedBlogs') || '[]')
          setHasLiked(likedBlogs.includes(data.data._id))
        } else {
          console.error("Blog not found:", data.error)
        }
      } catch (error) {
        console.error("Error fetching blog:", error)
      } finally {
        setLoading(false)
      }
    }

    const fetchComments = async () => {
      try {
        const response = await fetch(`/api/public/blogs/${slug}/comments`)
        const data = await response.json()

        if (data.success) {
          setComments(data.data)
        }
      } catch (error) {
        console.error("Error fetching comments:", error)
      } finally {
        setCommentsLoading(false)
      }
    }

    fetchBlog()
    fetchComments()
  }, [slug])

  const handleLike = async () => {
    try {
      const response = await fetch(`/api/public/blogs/${slug}/like`, {
        method: "POST",
      })

      const data = await response.json()

      if (data.success) {
        setBlog(prev => ({ ...prev, likes: data.data.likes }))
        setHasLiked(data.data.hasLiked)

        // Update localStorage
        const likedBlogs = JSON.parse(localStorage.getItem('likedBlogs') || '[]')
        if (data.data.hasLiked) {
          likedBlogs.push(blog._id)
        } else {
          const index = likedBlogs.indexOf(blog._id)
          if (index > -1) likedBlogs.splice(index, 1)
        }
        localStorage.setItem('likedBlogs', JSON.stringify(likedBlogs))

        toast.success(data.data.hasLiked ? "Blog beğenildi!" : "Beğeni kaldırıldı!")
      }
    } catch (error) {
      console.error("Error toggling like:", error)
      toast.error("Beğeni işlemi sırasında hata oluştu")
    }
  }

  const handleCommentSubmit = async (e) => {
    e.preventDefault()
    setIsSubmittingComment(true)

    try {
      const response = await fetch(`/api/public/blogs/${slug}/comments`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(commentForm),
      })

      const data = await response.json()

      if (data.success) {
        toast.success(data.message)
        setCommentForm({ name: "", email: "", comment: "" })
      } else {
        toast.error(data.error)
      }
    } catch (error) {
      console.error("Error submitting comment:", error)
      toast.error("Yorum gönderilirken hata oluştu")
    } finally {
      setIsSubmittingComment(false)
    }
  }

  const handleCommentChange = (e) => {
    const { name, value } = e.target
    setCommentForm(prev => ({ ...prev, [name]: value }))
  }

  const formatDate = (dateString) => {
    if (!dateString) return "Tarih belirtilmemiş"

    try {
      const date = new Date(dateString)

      // Geçersiz tarih kontrolü
      if (isNaN(date.getTime())) {
        console.error("Invalid date:", dateString)
        return "Geçersiz tarih"
      }

      return new Intl.DateTimeFormat("tr-TR", {
        day: "2-digit",
        month: "long",
        year: "numeric",
      }).format(date)
    } catch (error) {
      console.error("Date formatting error:", error, "Date string:", dateString)
      return "Tarih formatı hatası"
    }
  }

  const getCategoryName = (category) => {
    const categories = {
      "eğitim": "Eğitim",
      "teknoloji": "Teknoloji",
      "etkinlik": "Etkinlik",
      "haberler": "Haberler",
      "ipuçları": "İpuçları"
    }
    return categories[category] || category
  }

  if (loading) {
    return (
      <div className="container py-20">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-64 bg-gray-200 rounded mb-6"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!blog) {
    return (
      <div className="container py-20 text-center">
        <h1 className="text-3xl font-bold mb-4">Blog Yazısı Bulunamadı</h1>
        <p className="mb-8">Aradığınız blog yazısı mevcut değil veya kaldırılmış olabilir.</p>
        <Button asChild>
          <Link href="/blog">Tüm Blog Yazılarını Gör</Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container py-8">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <Button variant="ghost" className="mb-6" asChild>
            <Link href="/blog">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Blog'a Dön
            </Link>
          </Button>

          {/* Blog Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Card className="mb-8">
              {blog.featuredImage && (
                <div className="relative h-64 md:h-96 overflow-hidden rounded-t-lg">
                  <Image
                    src={blog.featuredImage}
                    alt={blog.title}
                    fill
                    className="object-cover"
                  />
                </div>
              )}
              <CardHeader className="pb-4">
                <div className="flex items-center gap-2 mb-4">
                  <Badge variant="secondary">
                    {getCategoryName(blog.category)}
                  </Badge>
                  {blog.tags && blog.tags.map((tag, index) => (
                    <Badge key={index} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>
                <CardTitle className="text-3xl md:text-4xl font-bold leading-tight">
                  {blog.title}
                </CardTitle>
                <div className="flex items-center gap-6 text-sm text-gray-500 dark:text-gray-400 mt-4">
                  <div className="flex items-center gap-1">
                    <User className="h-4 w-4" />
                    {blog.author}
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    {formatDate(blog.publishedAt)}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {blog.readTime} dk okuma
                  </div>
                </div>
              </CardHeader>
            </Card>
          </motion.div>

          {/* Blog Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="mb-8">
              <CardContent className="pt-6">
                <div className="prose prose-lg max-w-none dark:prose-invert">
                  <p className="text-xl text-gray-600 dark:text-gray-300 mb-6 font-medium">
                    {blog.excerpt}
                  </p>
                  <div className="whitespace-pre-wrap text-gray-700 dark:text-gray-300 leading-relaxed">
                    {blog.content}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Like and Share */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card className="mb-8">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <Button
                    variant={hasLiked ? "default" : "outline"}
                    onClick={handleLike}
                    className={hasLiked ? "bg-red-500 hover:bg-red-600" : ""}
                  >
                    <Heart className={`h-4 w-4 mr-2 ${hasLiked ? "fill-white" : ""}`} />
                    {blog.likes} Beğeni
                  </Button>
                  <div className="flex items-center gap-1 text-gray-500">
                    <MessageCircle className="h-4 w-4" />
                    {comments.length} Yorum
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Comments Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageCircle className="h-5 w-5" />
                  Yorumlar ({comments.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* Comment Form */}
                <form onSubmit={handleCommentSubmit} className="mb-8">
                  <h3 className="text-lg font-semibold mb-4">Yorum Yap</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <Label htmlFor="name">İsim</Label>
                      <Input
                        id="name"
                        name="name"
                        value={commentForm.name}
                        onChange={handleCommentChange}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">E-posta</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={commentForm.email}
                        onChange={handleCommentChange}
                        required
                      />
                    </div>
                  </div>
                  <div className="mb-4">
                    <Label htmlFor="comment">Yorumunuz</Label>
                    <Textarea
                      id="comment"
                      name="comment"
                      value={commentForm.comment}
                      onChange={handleCommentChange}
                      rows={4}
                      required
                    />
                  </div>
                  <Button type="submit" disabled={isSubmittingComment}>
                    <Send className="h-4 w-4 mr-2" />
                    {isSubmittingComment ? "Gönderiliyor..." : "Yorum Gönder"}
                  </Button>
                  <p className="text-sm text-gray-500 mt-2">
                    Yorumunuz onaylandıktan sonra görünecektir.
                  </p>
                </form>

                <Separator className="mb-6" />

                {/* Comments List */}
                <div className="space-y-6">
                  {commentsLoading ? (
                    <div className="text-center py-4">
                      <p>Yorumlar yükleniyor...</p>
                    </div>
                  ) : comments.length === 0 ? (
                    <div className="text-center py-8">
                      <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">Henüz yorum yapılmamış.</p>
                      <p className="text-gray-500">İlk yorumu siz yapın!</p>
                    </div>
                  ) : (
                    comments.map((comment, index) => (
                      <motion.div
                        key={comment._id || index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                        className="border-l-4 border-sky-200 pl-4 py-2"
                      >
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-semibold">{comment.name}</h4>
                          <span className="text-sm text-gray-500">
                            {formatDate(comment.createdAt)}
                          </span>
                        </div>
                        <p className="text-gray-700 dark:text-gray-300">
                          {comment.comment}
                        </p>
                      </motion.div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
