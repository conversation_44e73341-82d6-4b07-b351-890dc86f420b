// <PERSON><PERSON> <PERSON>, e<PERSON><PERSON> resimler için geçici çözüm sağlar
// Gerçek projenizde bu resimleri public klasörüne eklemelisiniz

export const getPlaceholderImage = (type: string) => {
  const baseUrl = "https://source.unsplash.com/random"

  switch (type) {
    case "logo":
      return "/placeholder.svg?height=40&width=40"
    case "logo-white":
      return "/placeholder.svg?height=40&width=40"
    case "hero":
      return `${baseUrl}/1920x1080?drone,sky`
    case "about":
      return `${baseUrl}/800x600?children,technology`
    case "about-mission":
      return `${baseUrl}/800x600?education,technology`
    case "about-philosophy":
      return `${baseUrl}/800x600?stem,education`
    case "program":
      return `${baseUrl}/800x600?drone,education`
    case "gallery":
      return `${baseUrl}/800x600?drone,workshop`
    case "instructor":
      return `${baseUrl}/400x400?portrait,professional`
    case "testimonial":
      return `${baseUrl}/400x400?portrait,person`
    default:
      return "/placeholder.svg?height=400&width=600"
  }
}
