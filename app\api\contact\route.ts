import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Contact from "@/lib/models/contact"
import { handleApiError } from "@/lib/api-utils"

/**
 * @swagger
 * /api/contact:
 *   post:
 *     summary: İletişim formu gönder
 *     description: Ziyaretçilerin iletişim formu göndermesi için kullanılır
 *     tags: [Public, Contacts]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - subject
 *               - message
 *             properties:
 *               name:
 *                 type: string
 *                 example: "Ahmet Yılmaz"
 *                 description: "Gönderen kişinin adı"
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *                 description: "Gönderen kişinin e-posta adresi"
 *               phone:
 *                 type: string
 *                 example: "05551234567"
 *                 description: "Gönderen kişinin telefon numarası (opsiyonel)"
 *               subject:
 *                 type: string
 *                 example: "Eğitim Hakkında Bilgi"
 *                 description: "Mesaj konusu"
 *               message:
 *                 type: string
 *                 example: "<PERSON>r<PERSON><PERSON>, eğitimleriniz hakkında bilgi almak istiyorum."
 *                 description: "Mesaj içeriği"
 *     responses:
 *       201:
 *         description: Mesaj başarıyla gönderildi
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Contact'
 *       400:
 *         description: Geçersiz veri
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Sunucu hatası
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function POST(request: Request) {
  try {
    await dbConnect()

    const body = await request.json()
    const { name, email, phone, subject, message } = body

    // Validation
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        { success: false, error: "Ad, e-posta, konu ve mesaj alanları gereklidir" },
        { status: 400 }
      )
    }

    const contact = await Contact.create({
      name,
      email,
      phone,
      subject,
      message,
    })

    return NextResponse.json({ success: true, data: contact }, { status: 201 })
  } catch (error) {
    console.error("Error creating contact:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
