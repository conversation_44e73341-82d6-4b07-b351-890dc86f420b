import { NextRequest, NextResponse } from "next/server"
import { writeFile, mkdir } from "fs/promises"
import { join } from "path"
import { existsSync } from "fs"

/**
 * @swagger
 * /api/upload:
 *   post:
 *     summary: Dosya yükle
 *     description: <PERSON>si<PERSON> (Admin yet<PERSON> gere<PERSON>)
 *     tags: [Admin, Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *               - type
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: "Yüklenecek resim dosyası (JPG, PNG, GIF, WebP - Max 5MB)"
 *               type:
 *                 type: string
 *                 enum: [profile, instructor, gallery, blog, logo]
 *                 description: "Dosya tipi (klasör belirlemek için)"
 *                 example: "gallery"
 *     responses:
 *       200:
 *         description: Dosya başarıyla yüklendi
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 url:
 *                   type: string
 *                   example: "/uploads/gallery/gallery_1234567890_abc123.jpg"
 *                   description: "Yüklenen dosyanın URL'si"
 *                 fileName:
 *                   type: string
 *                   example: "gallery_1234567890_abc123.jpg"
 *                   description: "Dosya adı"
 *                 size:
 *                   type: number
 *                   example: 1024000
 *                   description: "Dosya boyutu (byte)"
 *                 type:
 *                   type: string
 *                   example: "image/jpeg"
 *                   description: "MIME tipi"
 *       400:
 *         description: Geçersiz dosya
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Sunucu hatası
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const type = formData.get('type') as string || 'general'

    if (!file) {
      return NextResponse.json({ success: false, error: "Dosya bulunamadı" }, { status: 400 })
    }

    // Dosya boyutu kontrolü (5MB)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json({
        success: false,
        error: "Dosya boyutu 5MB'dan küçük olmalıdır"
      }, { status: 400 })
    }

    // Dosya tipi kontrolü
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({
        success: false,
        error: "Sadece resim dosyaları yüklenebilir"
      }, { status: 400 })
    }

    // Dosya uzantısını al
    const fileExtension = file.name.split('.').pop()?.toLowerCase()
    const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp']

    if (!fileExtension || !allowedExtensions.includes(fileExtension)) {
      return NextResponse.json({
        success: false,
        error: "Desteklenmeyen dosya formatı"
      }, { status: 400 })
    }

    // Benzersiz dosya adı oluştur
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const fileName = `${type}_${timestamp}_${randomString}.${fileExtension}`

    // Upload klasörünü oluştur
    const uploadDir = join(process.cwd(), 'public', 'uploads', type)

    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }

    // Dosyayı kaydet
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    const filePath = join(uploadDir, fileName)

    await writeFile(filePath, buffer)

    // Public URL oluştur
    const fileUrl = `/uploads/${type}/${fileName}`

    return NextResponse.json({
      success: true,
      url: fileUrl,
      fileName: fileName,
      size: file.size,
      type: file.type
    })
  } catch (error) {
    console.error("Error uploading file:", error)
    return NextResponse.json({
      success: false,
      error: "Dosya yüklenirken bir hata oluştu"
    }, { status: 500 })
  }
}
