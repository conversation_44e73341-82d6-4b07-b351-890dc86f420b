"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { toast } from "sonner"
import { Save, Plus, Trash, Settings, Camera, Upload, Loader2 } from "lucide-react"

export default function SiteSettingsPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(true)
  const [selectedLogoFile, setSelectedLogoFile] = useState(null)
  const [logoPreviewUrl, setLogoPreviewUrl] = useState("")
  const [isUploadingLogo, setIsUploadingLogo] = useState(false)
  const [formData, setFormData] = useState({
    siteName: "",
    siteDescription: "",
    siteLogo: "",
    contact: {
      phone: "",
      email: "",
      address: "",
      workingHours: "",
    },
    socialMedia: {
      facebook: "",
      instagram: "",
      twitter: "",
      youtube: "",
      linkedin: "",
      tiktok: "",
    },
    footer: {
      aboutText: "",
      quickLinks: [],
      services: [],
      copyrightText: "",
    },
    seo: {
      metaTitle: "",
      metaDescription: "",
      metaKeywords: "",
      ogImage: "",
    },
    settings: {
      maintenanceMode: false,
      allowRegistrations: true,
      allowTestimonials: true,
      allowComments: true,
    },
  })

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const response = await fetch("/api/admin/site-settings")
        const data = await response.json()

        if (data.success) {
          setFormData(data.data)
        } else {
          toast.error("Site ayarları yüklenirken hata oluştu")
        }
      } catch (error) {
        console.error("Error fetching settings:", error)
        toast.error("Site ayarları yüklenirken hata oluştu")
      } finally {
        setIsLoadingData(false)
      }
    }

    fetchSettings()
  }, [])

  const handleChange = (e) => {
    const { name, value } = e.target
    const keys = name.split('.')

    if (keys.length === 1) {
      setFormData(prev => ({ ...prev, [name]: value }))
    } else if (keys.length === 2) {
      setFormData(prev => ({
        ...prev,
        [keys[0]]: {
          ...prev[keys[0]],
          [keys[1]]: value
        }
      }))
    }
  }

  const handleSwitchChange = (name, checked) => {
    const keys = name.split('.')

    if (keys.length === 2) {
      setFormData(prev => ({
        ...prev,
        [keys[0]]: {
          ...prev[keys[0]],
          [keys[1]]: checked
        }
      }))
    }
  }

  const handleLinkChange = (section, index, field, value) => {
    setFormData(prev => ({
      ...prev,
      footer: {
        ...prev.footer,
        [section]: prev.footer[section].map((item, i) =>
          i === index ? { ...item, [field]: value } : item
        )
      }
    }))
  }

  const addLink = (section) => {
    setFormData(prev => ({
      ...prev,
      footer: {
        ...prev.footer,
        [section]: [
          ...prev.footer[section],
          { title: "", url: "", isActive: true }
        ]
      }
    }))
  }

  const removeLink = (section, index) => {
    setFormData(prev => ({
      ...prev,
      footer: {
        ...prev.footer,
        [section]: prev.footer[section].filter((_, i) => i !== index)
      }
    }))
  }

  const handleLogoFileChange = (e) => {
    const file = e.target.files[0]
    if (file) {
      // Dosya boyutu kontrolü (5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("Dosya boyutu 5MB'dan küçük olmalıdır.")
        return
      }

      // Dosya tipi kontrolü
      if (!file.type.startsWith('image/')) {
        toast.error("Sadece resim dosyaları yüklenebilir.")
        return
      }

      setSelectedLogoFile(file)

      // Preview oluştur
      const reader = new FileReader()
      reader.onload = (e) => {
        setLogoPreviewUrl(e.target.result)
      }
      reader.readAsDataURL(file)
    }
  }

  const uploadLogo = async (file) => {
    console.log("Logo: Upload image fonksiyonu çağrıldı:", file.name)

    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', 'logo')

    console.log("Logo: FormData hazırlandı")

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    })

    console.log("Logo: Upload response status:", response.status)
    const data = await response.json()
    console.log("Logo: Upload response data:", data)

    if (!data.success) {
      throw new Error(data.error || 'Logo yüklenirken hata oluştu')
    }

    return data.url
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)

    console.log("Site Settings: Form submit başladı")
    console.log("Site Settings: Form data:", formData)
    console.log("Site Settings: Selected logo file:", selectedLogoFile)

    try {
      let logoUrl = formData.siteLogo

      // Eğer yeni logo dosyası seçildiyse
      if (selectedLogoFile) {
        console.log("Site Settings: Logo yükleniyor...")
        setIsUploadingLogo(true)
        logoUrl = await uploadLogo(selectedLogoFile)
        console.log("Site Settings: Yüklenen logo URL:", logoUrl)
        setIsUploadingLogo(false)
      }

      const submitData = {
        ...formData,
        siteLogo: logoUrl
      }

      console.log("Site Settings: API'ye gönderilecek data:", submitData)

      const response = await fetch("/api/admin/site-settings", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      })

      const data = await response.json()

      if (data.success) {
        toast.success("Site ayarları başarıyla güncellendi.")
        // Form'u güncelle
        setFormData(prev => ({ ...prev, siteLogo: logoUrl }))
        // Dosya seçimini temizle
        setSelectedLogoFile(null)
        setLogoPreviewUrl("")
      } else {
        toast.error(data.error || "Site ayarları güncellenirken bir hata oluştu.")
      }
    } catch (error) {
      console.error("Error updating settings:", error)
      toast.error("Site ayarları güncellenirken bir hata oluştu.")
    } finally {
      setIsLoading(false)
      setIsUploadingLogo(false)
    }
  }

  if (isLoadingData) {
    return (
      <div className="container py-20">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-8"></div>
            <div className="space-y-6">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div>
      <div className="flex items-center gap-4 mb-8">
        <Settings className="h-8 w-8 text-sky-600" />
        <h1 className="text-3xl font-bold">Site Ayarları</h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Genel Bilgiler */}
        <Card>
          <CardHeader>
            <CardTitle>Genel Bilgiler</CardTitle>
            <CardDescription>Site hakkında temel bilgiler</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="siteName">Site Adı</Label>
                <Input
                  id="siteName"
                  name="siteName"
                  value={formData.siteName}
                  onChange={handleChange}
                  placeholder="Uçuş Atölyesi"
                />
              </div>
              {/* Logo Yükleme */}
              <div className="space-y-4">
                <Label>Site Logosu</Label>

                {/* Dosya Seçici */}
                <div className="flex flex-col items-center space-y-4 p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-sky-400 transition-colors">
                  {logoPreviewUrl || formData.siteLogo ? (
                    <div className="relative">
                      <img
                        src={logoPreviewUrl || formData.siteLogo}
                        alt="Logo Preview"
                        className="w-full max-w-xs h-24 object-contain rounded-lg bg-gray-50 p-2"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="absolute top-2 right-2"
                        onClick={() => {
                          setSelectedLogoFile(null)
                          setLogoPreviewUrl("")
                        }}
                      >
                        Değiştir
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center">
                      <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-lg font-medium text-gray-700 mb-2">Logo Yükle</p>
                      <p className="text-sm text-gray-500 mb-4">
                        PNG, JPG veya SVG formatında, maksimum 5MB
                      </p>
                    </div>
                  )}

                  <Label htmlFor="logoFile" className="cursor-pointer">
                    <div className="flex items-center gap-2 px-4 py-2 bg-sky-50 hover:bg-sky-100 rounded-lg border border-sky-200 transition-colors">
                      <Camera className="h-4 w-4 text-sky-600" />
                      <span className="text-sm text-sky-600">
                        {logoPreviewUrl || formData.siteLogo ? "Farklı Logo Seç" : "Logo Seç"}
                      </span>
                    </div>
                  </Label>
                  <Input
                    id="logoFile"
                    type="file"
                    accept="image/*"
                    onChange={handleLogoFileChange}
                    className="hidden"
                  />
                </div>

                {isUploadingLogo && (
                  <div className="flex items-center justify-center gap-2 text-sky-600">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm">Logo yükleniyor...</span>
                  </div>
                )}

                {formData.siteLogo && !logoPreviewUrl && (
                  <div className="text-xs text-gray-500 text-center">
                    Mevcut logo: {formData.siteLogo}
                  </div>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="siteDescription">Site Açıklaması</Label>
              <Textarea
                id="siteDescription"
                name="siteDescription"
                value={formData.siteDescription}
                onChange={handleChange}
                placeholder="Çocuklar için STEM tabanlı uçuş ve teknoloji atölyesi"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* İletişim Bilgileri */}
        <Card>
          <CardHeader>
            <CardTitle>İletişim Bilgileri</CardTitle>
            <CardDescription>Footer'da görünecek iletişim bilgileri</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contact.phone">Telefon</Label>
                <Input
                  id="contact.phone"
                  name="contact.phone"
                  value={formData.contact.phone}
                  onChange={handleChange}
                  placeholder="+90 (212) 123 45 67"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contact.email">E-posta</Label>
                <Input
                  id="contact.email"
                  name="contact.email"
                  type="email"
                  value={formData.contact.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="contact.address">Adres</Label>
              <Input
                id="contact.address"
                name="contact.address"
                value={formData.contact.address}
                onChange={handleChange}
                placeholder="İstanbul, Türkiye"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="contact.workingHours">Çalışma Saatleri</Label>
              <Input
                id="contact.workingHours"
                name="contact.workingHours"
                value={formData.contact.workingHours}
                onChange={handleChange}
                placeholder="Pazartesi - Cuma: 09:00 - 18:00"
              />
            </div>
          </CardContent>
        </Card>

        {/* Sosyal Medya */}
        <Card>
          <CardHeader>
            <CardTitle>Sosyal Medya Linkleri</CardTitle>
            <CardDescription>Footer'da görünecek sosyal medya linkleri</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="socialMedia.facebook">Facebook</Label>
                <Input
                  id="socialMedia.facebook"
                  name="socialMedia.facebook"
                  value={formData.socialMedia.facebook}
                  onChange={handleChange}
                  placeholder="https://facebook.com/ucusatolyesi"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="socialMedia.instagram">Instagram</Label>
                <Input
                  id="socialMedia.instagram"
                  name="socialMedia.instagram"
                  value={formData.socialMedia.instagram}
                  onChange={handleChange}
                  placeholder="https://instagram.com/ucusatolyesi"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="socialMedia.twitter">Twitter</Label>
                <Input
                  id="socialMedia.twitter"
                  name="socialMedia.twitter"
                  value={formData.socialMedia.twitter}
                  onChange={handleChange}
                  placeholder="https://twitter.com/ucusatolyesi"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="socialMedia.youtube">YouTube</Label>
                <Input
                  id="socialMedia.youtube"
                  name="socialMedia.youtube"
                  value={formData.socialMedia.youtube}
                  onChange={handleChange}
                  placeholder="https://youtube.com/@ucusatolyesi"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="socialMedia.linkedin">LinkedIn</Label>
                <Input
                  id="socialMedia.linkedin"
                  name="socialMedia.linkedin"
                  value={formData.socialMedia.linkedin}
                  onChange={handleChange}
                  placeholder="https://linkedin.com/company/ucusatolyesi"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="socialMedia.tiktok">TikTok</Label>
                <Input
                  id="socialMedia.tiktok"
                  name="socialMedia.tiktok"
                  value={formData.socialMedia.tiktok}
                  onChange={handleChange}
                  placeholder="https://tiktok.com/@ucusatolyesi"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer Ayarları */}
        <Card>
          <CardHeader>
            <CardTitle>Footer Ayarları</CardTitle>
            <CardDescription>Footer'da görünecek içerikler</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="footer.aboutText">Hakkımızda Metni</Label>
              <Textarea
                id="footer.aboutText"
                name="footer.aboutText"
                value={formData.footer.aboutText}
                onChange={handleChange}
                placeholder="Site hakkında kısa açıklama..."
                rows={4}
              />
            </div>

            <Separator />

            {/* Hızlı Linkler */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-base font-semibold">Hızlı Linkler</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => addLink('quickLinks')}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Link Ekle
                </Button>
              </div>
              <div className="space-y-3">
                {formData.footer.quickLinks.map((link, index) => (
                  <div key={index} className="flex gap-2 items-center">
                    <Input
                      placeholder="Link başlığı"
                      value={link.title}
                      onChange={(e) => handleLinkChange('quickLinks', index, 'title', e.target.value)}
                    />
                    <Input
                      placeholder="URL"
                      value={link.url}
                      onChange={(e) => handleLinkChange('quickLinks', index, 'url', e.target.value)}
                    />
                    <Switch
                      checked={link.isActive}
                      onCheckedChange={(checked) => handleLinkChange('quickLinks', index, 'isActive', checked)}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeLink('quickLinks', index)}
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Hizmetler */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-base font-semibold">Hizmetler</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => addLink('services')}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Hizmet Ekle
                </Button>
              </div>
              <div className="space-y-3">
                {formData.footer.services.map((service, index) => (
                  <div key={index} className="flex gap-2 items-center">
                    <Input
                      placeholder="Hizmet adı"
                      value={service.title}
                      onChange={(e) => handleLinkChange('services', index, 'title', e.target.value)}
                    />
                    <Input
                      placeholder="URL"
                      value={service.url}
                      onChange={(e) => handleLinkChange('services', index, 'url', e.target.value)}
                    />
                    <Switch
                      checked={service.isActive}
                      onCheckedChange={(checked) => handleLinkChange('services', index, 'isActive', checked)}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeLink('services', index)}
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <Label htmlFor="footer.copyrightText">Telif Hakkı Metni</Label>
              <Input
                id="footer.copyrightText"
                name="footer.copyrightText"
                value={formData.footer.copyrightText}
                onChange={handleChange}
                placeholder="© 2024 Uçuş Atölyesi. Tüm hakları saklıdır."
              />
            </div>
          </CardContent>
        </Card>

        {/* Site Ayarları */}
        <Card>
          <CardHeader>
            <CardTitle>Site Ayarları</CardTitle>
            <CardDescription>Genel site ayarları</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="settings.allowRegistrations"
                  checked={formData.settings.allowRegistrations}
                  onCheckedChange={(checked) => handleSwitchChange("settings.allowRegistrations", checked)}
                />
                <Label htmlFor="settings.allowRegistrations">Kayıtlara İzin Ver</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="settings.allowTestimonials"
                  checked={formData.settings.allowTestimonials}
                  onCheckedChange={(checked) => handleSwitchChange("settings.allowTestimonials", checked)}
                />
                <Label htmlFor="settings.allowTestimonials">Yorumlara İzin Ver</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="settings.allowComments"
                  checked={formData.settings.allowComments}
                  onCheckedChange={(checked) => handleSwitchChange("settings.allowComments", checked)}
                />
                <Label htmlFor="settings.allowComments">Blog Yorumlarına İzin Ver</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="settings.maintenanceMode"
                  checked={formData.settings.maintenanceMode}
                  onCheckedChange={(checked) => handleSwitchChange("settings.maintenanceMode", checked)}
                />
                <Label htmlFor="settings.maintenanceMode">Bakım Modu</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading} className="bg-sky-500 hover:bg-sky-600">
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? "Kaydediliyor..." : "Ayarları Kaydet"}
          </Button>
        </div>
      </form>
    </div>
  )
}
