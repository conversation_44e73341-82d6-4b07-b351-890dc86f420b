server {
    listen 80;
    server_name www.ucusatolyesi.com.tr ucusatolyesi.com.tr;
    
    # HTTP'den HTTPS'e yönlendirme
    return 301 https://www.ucusatolyesi.com.tr$request_uri;
}

server {
    listen 443 ssl http2;
    server_name www.ucusatolyesi.com.tr;
    
    # SSL sertifikaları (Let's Encrypt veya başka bir CA'dan)
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    
    # SSL ayarları
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # Güvenlik başlıkları
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Gzip sıkıştırma
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;
    
    # Static dosyalar için cache
    location /_next/static {
        alias /path/to/your/app/.next/static;
        expires 365d;
        access_log off;
    }
    
    location /static {
        alias /path/to/your/app/public;
        expires 30d;
        access_log off;
    }
    
    # Ana uygulama
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
}

# ucusatolyesi.com.tr'den www'ye yönlendirme
server {
    listen 443 ssl http2;
    server_name ucusatolyesi.com.tr;
    
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    
    return 301 https://www.ucusatolyesi.com.tr$request_uri;
}
