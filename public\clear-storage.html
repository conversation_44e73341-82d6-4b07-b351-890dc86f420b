<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Storage Temizleme - U<PERSON>uş Atölyesi</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #0ea5e9, #3b82f6);
            color: white;
            text-align: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        button {
            background: #ef4444;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s;
        }
        button:hover {
            background: #dc2626;
        }
        .success {
            background: #10b981;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }
        .info {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚁 Uçuş Atölyesi</h1>
        <h2>Storage Temizleme Aracı</h2>
        
        <div class="info">
            <p><strong>Ne zaman kullanılır?</strong></p>
            <p>• Admin paneline giriş yapamıyorsanız</p>
            <p>• NextAuth secret key değiştirdiyseniz</p>
            <p>• Session sorunları yaşıyorsanız</p>
            <p>• Cache problemleri varsa</p>
        </div>

        <button onclick="clearAllStorage()">
            🗑️ Tüm Storage'ı Temizle
        </button>
        
        <button onclick="clearCookies()">
            🍪 Sadece Cookies Temizle
        </button>
        
        <button onclick="clearLocalStorage()">
            💾 Sadece LocalStorage Temizle
        </button>

        <div id="success" class="success">
            ✅ Storage başarıyla temizlendi! Sayfayı yenileyin.
        </div>

        <div class="info">
            <p><strong>Temizlendikten sonra:</strong></p>
            <p>1. Bu sayfayı kapatın</p>
            <p>2. Ana sayfaya gidin: <a href="/" style="color: #fbbf24;">localhost:3000</a></p>
            <p>3. Admin paneline giriş yapın: <a href="/admin/dashboard" style="color: #fbbf24;">Admin Panel</a></p>
        </div>
    </div>

    <script>
        function clearAllStorage() {
            // LocalStorage temizle
            localStorage.clear();
            
            // SessionStorage temizle
            sessionStorage.clear();
            
            // Cookies temizle
            document.cookie.split(";").forEach(function(c) { 
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
            });
            
            // IndexedDB temizle (eğer varsa)
            if ('indexedDB' in window) {
                indexedDB.databases().then(databases => {
                    databases.forEach(db => {
                        indexedDB.deleteDatabase(db.name);
                    });
                });
            }
            
            showSuccess();
            console.log('Tüm storage temizlendi');
        }

        function clearCookies() {
            document.cookie.split(";").forEach(function(c) { 
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
            });
            showSuccess();
            console.log('Cookies temizlendi');
        }

        function clearLocalStorage() {
            localStorage.clear();
            sessionStorage.clear();
            showSuccess();
            console.log('LocalStorage temizlendi');
        }

        function showSuccess() {
            document.getElementById('success').style.display = 'block';
            setTimeout(() => {
                document.getElementById('success').style.display = 'none';
            }, 5000);
        }

        // Sayfa yüklendiğinde mevcut storage bilgilerini göster
        window.onload = function() {
            console.log('Mevcut LocalStorage:', localStorage);
            console.log('Mevcut Cookies:', document.cookie);
        }
    </script>
</body>
</html>
