"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { toast } from "sonner"
import { User, Camera, Save, Eye, EyeOff } from "lucide-react"

export default function AdminProfilePage() {
  const { data: session, update } = useSession()
  const [isLoading, setIsLoading] = useState(false)
  const [isPasswordLoading, setIsPasswordLoading] = useState(false)
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const [profileData, setProfileData] = useState({
    name: "",
    email: "",
    profileImage: "",
  })

  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  })

  const [selectedFile, setSelectedFile] = useState(null)
  const [previewUrl, setPreviewUrl] = useState("")
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        console.log("useEffect: Profil verisi çekiliyor...")
        const response = await fetch('/api/admin/profile')
        const data = await response.json()

        console.log("useEffect: API response:", data)

        if (data.success) {
          const newProfileData = {
            name: data.data.name || "",
            email: data.data.email || "",
            profileImage: data.data.profileImage || "",
          }
          console.log("useEffect: Setting profile data:", newProfileData)
          setProfileData(newProfileData)
          setPreviewUrl(data.data.profileImage || "")
          setIsInitialized(true)
        }
      } catch (error) {
        console.error('Error fetching profile:', error)
        // Fallback to session data
        if (session?.user) {
          const fallbackData = {
            name: session.user.name || "",
            email: session.user.email || "",
            profileImage: session.user.image || "",
          }
          console.log("useEffect: Using fallback data:", fallbackData)
          setProfileData(fallbackData)
          setPreviewUrl(session.user.image || "")
          setIsInitialized(true)
        }
      }
    }

    // Sadece ilk yüklemede çalıştır
    if (session && !isInitialized) {
      console.log("useEffect: İlk yükleme, profil verisi çekiliyor")
      fetchProfile()
    }
  }, [session, isInitialized])

  const handleProfileChange = (e) => {
    const { name, value } = e.target
    console.log("Profile change:", name, "=", value)
    setProfileData(prev => {
      const newData = { ...prev, [name]: value }
      console.log("New profile data:", newData)
      return newData
    })
  }

  const handlePasswordChange = (e) => {
    const { name, value } = e.target
    setPasswordData(prev => ({ ...prev, [name]: value }))
  }

  const handleFileChange = (e) => {
    const file = e.target.files?.[0]
    if (file) {
      // Dosya boyutu kontrolü (5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("Dosya boyutu 5MB'dan küçük olmalıdır.")
        return
      }

      // Dosya tipi kontrolü
      if (!file.type.startsWith('image/')) {
        toast.error("Lütfen geçerli bir resim dosyası seçin.")
        return
      }

      setSelectedFile(file)

      // Preview oluştur
      const reader = new FileReader()
      reader.onload = (e) => {
        setPreviewUrl(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const uploadImage = async (file) => {
    console.log("Upload image fonksiyonu çağrıldı:", file.name)

    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', 'profile')

    console.log("FormData hazırlandı")

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    })

    console.log("Upload response status:", response.status)
    const data = await response.json()
    console.log("Upload response data:", data)

    if (!data.success) {
      throw new Error(data.error || 'Resim yüklenirken hata oluştu')
    }

    return data.url
  }

  const handleProfileSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)

    console.log("Form submit başladı")
    console.log("Profile data:", profileData)
    console.log("Selected file:", selectedFile)

    try {
      let imageUrl = profileData.profileImage

      // Eğer yeni resim seçildiyse yükle
      if (selectedFile) {
        console.log("Resim yükleniyor...")
        imageUrl = await uploadImage(selectedFile)
        console.log("Yüklenen resim URL:", imageUrl)
      }

      const updateData = {
        name: profileData.name,
        email: profileData.email,
        profileImage: imageUrl,
      }

      console.log("API'ye gönderilecek data:", updateData)

      const response = await fetch('/api/admin/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      })

      console.log("API response status:", response.status)
      const data = await response.json()
      console.log("API response data:", data)
      console.log("API response data.data:", data.data)

      if (data.success) {
        console.log("API başarılı, state güncelleniyor...")

        // Profil verilerini güncelle
        const newProfileData = {
          name: data.data.name || profileData.name,
          email: data.data.email || profileData.email,
          profileImage: data.data.profileImage || imageUrl,
        }

        console.log("Yeni profil data:", newProfileData)
        setProfileData(newProfileData)

        // State güncellemesini doğrula
        setTimeout(() => {
          console.log("State güncelleme sonrası profileData:", profileData)
        }, 100)

        // Preview URL'yi güncelle
        const newPreviewUrl = data.data.profileImage || imageUrl
        console.log("Yeni preview URL:", newPreviewUrl)
        setPreviewUrl(newPreviewUrl)

        // Session'ı güncelle
        if (update) {
          console.log("Session güncelleniyor...")
          const newSessionData = {
            ...session,
            user: {
              ...session?.user,
              name: data.data.name || profileData.name,
              email: data.data.email || profileData.email,
              image: data.data.profileImage || imageUrl,
            },
          }
          console.log("Yeni session data:", newSessionData)
          await update(newSessionData)
          console.log("Session güncellendi")
        }

        setSelectedFile(null)
        console.log("Toast gösteriliyor...")
        toast.success("Profil bilgileri başarıyla güncellendi.")
        console.log("İşlem tamamlandı")

        // Navigation'ı güncellemek için custom event gönder
        window.dispatchEvent(new CustomEvent('profileUpdated', {
          detail: {
            name: data.data.name,
            email: data.data.email,
            image: data.data.profileImage
          }
        }))

        // Development modunda localStorage'a da kaydet
        if (process.env.NODE_ENV === "development") {
          const devUserData = {
            id: "dev-admin-id",
            name: data.data.name,
            email: data.data.email,
            role: "admin",
            image: data.data.profileImage
          }
          localStorage.setItem('devUserData', JSON.stringify(devUserData))
          console.log("Profile: Dev user data saved to localStorage:", devUserData)
        }
      } else {
        console.error("API error:", data.error)
        toast.error(data.error || "Profil güncellenirken bir hata oluştu.")
      }
    } catch (error) {
      console.error("Error updating profile:", error)
      toast.error("Profil güncellenirken bir hata oluştu.")
    } finally {
      setIsLoading(false)
    }
  }

  const handlePasswordSubmit = async (e) => {
    e.preventDefault()
    setIsPasswordLoading(true)

    // Şifre validasyonu
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error("Yeni şifreler eşleşmiyor.")
      setIsPasswordLoading(false)
      return
    }

    if (passwordData.newPassword.length < 6) {
      toast.error("Yeni şifre en az 6 karakter olmalıdır.")
      setIsPasswordLoading(false)
      return
    }

    try {
      const response = await fetch('/api/admin/profile/password', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword,
        }),
      })

      const data = await response.json()

      if (data.success) {
        setPasswordData({
          currentPassword: "",
          newPassword: "",
          confirmPassword: "",
        })
        toast.success("Şifre başarıyla güncellendi.")
      } else {
        toast.error(data.error || "Şifre güncellenirken bir hata oluştu.")
      }
    } catch (error) {
      console.error("Error updating password:", error)
      toast.error("Şifre güncellenirken bir hata oluştu.")
    } finally {
      setIsPasswordLoading(false)
    }
  }

  const getInitials = (name) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <div>
      <div className="flex items-center gap-4 mb-8">
        <User className="h-8 w-8 text-sky-600" />
        <h1 className="text-3xl font-bold">Profil Ayarları</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Profil Bilgileri */}
        <Card>
          <CardHeader>
            <CardTitle>Profil Bilgileri</CardTitle>
            <CardDescription>
              Kişisel bilgilerinizi ve profil resminizi güncelleyin.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleProfileSubmit} className="space-y-6">
              {/* Profil Resmi */}
              <div className="flex flex-col items-center space-y-4">
                <Avatar className="h-24 w-24">
                  {previewUrl ? (
                    <AvatarImage src={previewUrl} alt="Profil resmi" />
                  ) : null}
                  <AvatarFallback className="text-lg">
                    {getInitials(profileData.name || "Admin")}
                  </AvatarFallback>
                </Avatar>

                <div className="flex flex-col items-center space-y-2">
                  <Label htmlFor="profileImage" className="cursor-pointer">
                    <div className="flex items-center gap-2 px-4 py-2 bg-sky-50 hover:bg-sky-100 rounded-lg border border-sky-200 transition-colors">
                      <Camera className="h-4 w-4 text-sky-600" />
                      <span className="text-sm text-sky-600">Resim Seç</span>
                    </div>
                  </Label>
                  <Input
                    id="profileImage"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                  <p className="text-xs text-gray-500 text-center">
                    JPG, PNG veya GIF. Maksimum 5MB.
                  </p>
                </div>
              </div>

              <Separator />

              {/* İsim */}
              <div className="space-y-2">
                <Label htmlFor="name">Ad Soyad</Label>
                <Input
                  id="name"
                  name="name"
                  value={profileData.name}
                  onChange={handleProfileChange}
                  placeholder="Adınız ve soyadınız"
                  required
                />
                <p className="text-xs text-gray-500">Mevcut: {profileData.name}</p>
              </div>

              {/* E-posta */}
              <div className="space-y-2">
                <Label htmlFor="email">E-posta</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={profileData.email}
                  onChange={handleProfileChange}
                  placeholder="E-posta adresiniz"
                  required
                />
                <p className="text-xs text-gray-500">Mevcut: {profileData.email}</p>
              </div>

              <Button type="submit" disabled={isLoading} className="w-full bg-sky-500 hover:bg-sky-600">
                <Save className="h-4 w-4 mr-2" />
                {isLoading ? "Kaydediliyor..." : "Profili Güncelle"}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Şifre Değiştirme */}
        <Card>
          <CardHeader>
            <CardTitle>Şifre Değiştir</CardTitle>
            <CardDescription>
              Hesabınızın güvenliği için şifrenizi güncelleyin.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handlePasswordSubmit} className="space-y-6">
              {/* Mevcut Şifre */}
              <div className="space-y-2">
                <Label htmlFor="currentPassword">Mevcut Şifre</Label>
                <div className="relative">
                  <Input
                    id="currentPassword"
                    name="currentPassword"
                    type={showCurrentPassword ? "text" : "password"}
                    value={passwordData.currentPassword}
                    onChange={handlePasswordChange}
                    placeholder="Mevcut şifreniz"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  >
                    {showCurrentPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              {/* Yeni Şifre */}
              <div className="space-y-2">
                <Label htmlFor="newPassword">Yeni Şifre</Label>
                <div className="relative">
                  <Input
                    id="newPassword"
                    name="newPassword"
                    type={showNewPassword ? "text" : "password"}
                    value={passwordData.newPassword}
                    onChange={handlePasswordChange}
                    placeholder="Yeni şifreniz"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                  >
                    {showNewPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              {/* Şifre Tekrar */}
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Yeni Şifre (Tekrar)</Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    value={passwordData.confirmPassword}
                    onChange={handlePasswordChange}
                    placeholder="Yeni şifrenizi tekrar girin"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="text-sm text-gray-500">
                <p>Şifre gereksinimleri:</p>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>En az 6 karakter</li>
                  <li>Güvenlik için karmaşık bir şifre kullanın</li>
                </ul>
              </div>

              <Button type="submit" disabled={isPasswordLoading} className="w-full bg-red-500 hover:bg-red-600">
                <Save className="h-4 w-4 mr-2" />
                {isPasswordLoading ? "Güncelleniyor..." : "Şifreyi Güncelle"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
