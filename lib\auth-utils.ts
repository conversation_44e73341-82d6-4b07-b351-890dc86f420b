import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { NextRequest } from "next/server"

// Development modunda kullanıcı verilerini saklamak için
let devUserData = {
  id: "dev-admin-id",
  name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
  email: "<EMAIL>",
  role: "admin",
  image: ""
}

// localStorage'dan dev user data'yı yükle
function loadDevUserData() {
  if (typeof window !== 'undefined' && process.env.NODE_ENV === "development") {
    const stored = localStorage.getItem('devUserData')
    if (stored) {
      try {
        devUserData = { ...devUserData, ...JSON.parse(stored) }
        console.log("Dev user data loaded from localStorage:", devUserData)
      } catch (error) {
        console.error("Error loading dev user data:", error)
      }
    }
  }
}

// localStorage'a dev user data'yı kaydet
function saveDevUserData() {
  if (typeof window !== 'undefined' && process.env.NODE_ENV === "development") {
    localStorage.setItem('devUserData', JSON.stringify(devUserData))
    console.log("Dev user data saved to localStorage:", devUserData)
  }
}

// Server-side session kontrolü
export async function getAuthSession() {
  try {
    const session = await getServerSession(authOptions)
    return session
  } catch (error) {
    console.error("Error getting auth session:", error)
    return null
  }
}

// Admin yetkisi kontrolü
export async function checkAdminAuth() {
  const session = await getAuthSession()

  console.log("checkAdminAuth - Session:", session)

  if (!session || !session.user) {
    // Development modunda mock user döndür
    if (process.env.NODE_ENV === "development") {
      // localStorage'dan güncel veriyi yükle
      loadDevUserData()
      return {
        authorized: true,
        user: devUserData
      }
    }
    console.log("checkAdminAuth - No session, access denied")
    return { authorized: false, error: "Oturum bulunamadı" }
  }

  if (session.user.role !== 'admin' && session.user.role !== 'editor') {
    return { authorized: false, error: "Yetkisiz erişim" }
  }

  return { authorized: true, user: session.user }
}

// Development modunda kullanıcı verilerini güncelle
export function updateDevUserData(userData: Partial<typeof devUserData>) {
  if (process.env.NODE_ENV === "development") {
    devUserData = { ...devUserData, ...userData }
    saveDevUserData() // localStorage'a kaydet
    console.log("Dev user data updated and saved:", devUserData)
  }
}

// Development modunda kullanıcı verilerini al
export function getDevUserData() {
  if (process.env.NODE_ENV === "development") {
    loadDevUserData() // localStorage'dan güncel veriyi yükle
    return devUserData
  }
  return null
}

// API route'ları için auth middleware
export async function withAuth(handler: Function) {
  return async (request: NextRequest, context?: any) => {
    const authCheck = await checkAdminAuth()

    if (!authCheck.authorized) {
      return Response.json(
        { success: false, error: authCheck.error },
        { status: 401 }
      )
    }

    // Auth başarılı, handler'ı çalıştır
    return handler(request, context)
  }
}

// Request'ten session bilgisi alma
export async function getSessionFromRequest(request: NextRequest) {
  try {
    // Cookie'den session token'ını al
    const sessionToken = request.cookies.get('next-auth.session-token')?.value ||
                        request.cookies.get('__Secure-next-auth.session-token')?.value

    if (!sessionToken) {
      return null
    }

    // Session'ı doğrula
    const session = await getAuthSession()
    return session
  } catch (error) {
    console.error("Error getting session from request:", error)
    return null
  }
}

// Role-based access control
export function hasPermission(userRole: string, requiredRole: string) {
  const roleHierarchy = {
    'admin': 3,
    'editor': 2,
    'user': 1
  }

  const userLevel = roleHierarchy[userRole] || 0
  const requiredLevel = roleHierarchy[requiredRole] || 0

  return userLevel >= requiredLevel
}

// Admin API wrapper
export function createAuthenticatedHandler(handler: Function, requiredRole: string = 'editor') {
  return async (request: NextRequest, context?: any) => {
    try {
      const authCheck = await checkAdminAuth()

      if (!authCheck.authorized) {
        return Response.json(
          { success: false, error: "Kimlik doğrulama gerekli" },
          { status: 401 }
        )
      }

      // Role kontrolü
      if (!hasPermission(authCheck.user.role, requiredRole)) {
        return Response.json(
          { success: false, error: "Bu işlem için yeterli yetkiniz yok" },
          { status: 403 }
        )
      }

      // Request'e user bilgisini ekle
      (request as any).user = authCheck.user

      return handler(request, context)
    } catch (error) {
      console.error("Auth handler error:", error)
      return Response.json(
        { success: false, error: "Kimlik doğrulama hatası" },
        { status: 500 }
      )
    }
  }
}
