// YouTube URL'den video ID'sini çıkarma
export function extractYouTubeVideoId(url: string): string | null {
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/)([^&\n?#]+)/,
    /^([a-zA-Z0-9_-]{11})$/ // Direct video ID
  ]
  
  for (const pattern of patterns) {
    const match = url.match(pattern)
    if (match) {
      return match[1]
    }
  }
  
  return null
}

// YouTube video bilgilerini çekme (YouTube Data API olmadan)
export async function getYouTubeVideoInfo(videoId: string) {
  try {
    // YouTube oEmbed API kullanarak temel bilgileri al
    const oEmbedResponse = await fetch(
      `https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${videoId}&format=json`
    )
    
    if (!oEmbedResponse.ok) {
      throw new Error('Video bulunamadı')
    }
    
    const oEmbedData = await oEmbedResponse.json()
    
    // Thumbnail URL'lerini oluştur
    const thumbnails = {
      default: `https://img.youtube.com/vi/${videoId}/default.jpg`,
      medium: `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,
      high: `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
      maxres: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
    }
    
    return {
      videoId,
      title: oEmbedData.title,
      description: '', // oEmbed'de description yok
      thumbnail: thumbnails.maxres,
      thumbnails,
      duration: '', // oEmbed'de duration yok
      channelTitle: oEmbedData.author_name,
      url: `https://www.youtube.com/watch?v=${videoId}`,
      embedUrl: `https://www.youtube.com/embed/${videoId}`,
    }
  } catch (error) {
    console.error('YouTube video info fetch error:', error)
    throw new Error('Video bilgileri alınamadı')
  }
}

// YouTube URL'sini doğrulama
export function isValidYouTubeUrl(url: string): boolean {
  const videoId = extractYouTubeVideoId(url)
  return videoId !== null && videoId.length === 11
}

// YouTube embed URL'si oluşturma
export function createYouTubeEmbedUrl(videoId: string, options?: {
  autoplay?: boolean
  mute?: boolean
  controls?: boolean
  start?: number
}): string {
  const params = new URLSearchParams()
  
  if (options?.autoplay) params.append('autoplay', '1')
  if (options?.mute) params.append('mute', '1')
  if (options?.controls === false) params.append('controls', '0')
  if (options?.start) params.append('start', options.start.toString())
  
  const queryString = params.toString()
  return `https://www.youtube.com/embed/${videoId}${queryString ? `?${queryString}` : ''}`
}

// YouTube thumbnail URL'si oluşturma
export function getYouTubeThumbnail(videoId: string, quality: 'default' | 'medium' | 'high' | 'maxres' = 'maxres'): string {
  const qualityMap = {
    default: 'default',
    medium: 'mqdefault',
    high: 'hqdefault',
    maxres: 'maxresdefault',
  }
  
  return `https://img.youtube.com/vi/${videoId}/${qualityMap[quality]}.jpg`
}
