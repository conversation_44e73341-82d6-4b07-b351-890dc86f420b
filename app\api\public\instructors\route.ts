import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Instructor from "@/lib/models/instructor"

export async function GET() {
  try {
    await dbConnect()

    const instructors = await Instructor.find({ isActive: true })
      .select('name role bio image expertise experience email phone social')
      .sort({ createdAt: -1 })

    return NextResponse.json({ success: true, data: instructors })
  } catch (error) {
    console.error("Error fetching public instructors:", error)
    return NextResponse.json({ success: false, error: "Eğitmenler yüklenirken hata oluştu" }, { status: 500 })
  }
}
