#!/bin/bash

# Uçuş Atölyesi Production Deployment Script
# Domain: https://www.ucusatolyesi.com.tr

echo "🚁 Uçuş Atölyesi Production Deployment Başlıyor..."

# Renklendirme
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Hata durumunda çık
set -e

# Environment kontrol
echo -e "${YELLOW}📋 Environment kontrol ediliyor...${NC}"
if [ ! -f ".env.production" ]; then
    echo -e "${RED}❌ .env.production dosyası bulunamadı!${NC}"
    exit 1
fi

# Dependencies yükle
echo -e "${YELLOW}📦 Dependencies yükleniyor...${NC}"
npm install

# Build
echo -e "${YELLOW}🔨 Uygulama build ediliyor...${NC}"
npm run build

# PM2 ile deploy (eğer PM2 kuruluysa)
if command -v pm2 &> /dev/null; then
    echo -e "${YELLOW}🚀 PM2 ile deploy ediliyor...${NC}"
    
    # Mevcut uygulamayı durdur
    pm2 stop ucus-atolyesi 2>/dev/null || true
    pm2 delete ucus-atolyesi 2>/dev/null || true
    
    # Yeni uygulamayı başlat
    pm2 start ecosystem.config.js
    pm2 save
    
    echo -e "${GREEN}✅ PM2 deployment tamamlandı!${NC}"
    
elif command -v docker &> /dev/null; then
    echo -e "${YELLOW}🐳 Docker ile deploy ediliyor...${NC}"
    
    # Docker build ve run
    docker-compose down 2>/dev/null || true
    docker-compose up -d --build
    
    echo -e "${GREEN}✅ Docker deployment tamamlandı!${NC}"
    
else
    echo -e "${YELLOW}🔧 Manuel deployment...${NC}"
    echo -e "${YELLOW}Lütfen aşağıdaki komutu çalıştırın:${NC}"
    echo "NODE_ENV=production npm start"
fi

# Health check
echo -e "${YELLOW}🏥 Health check yapılıyor...${NC}"
sleep 5

if curl -f -s https://www.ucusatolyesi.com.tr/api/health > /dev/null; then
    echo -e "${GREEN}✅ Site başarıyla çalışıyor!${NC}"
    echo -e "${GREEN}🌐 https://www.ucusatolyesi.com.tr${NC}"
else
    echo -e "${RED}❌ Health check başarısız! Logları kontrol edin.${NC}"
fi

# Setup hatırlatması
echo -e "${YELLOW}📝 İlk kurulum için:${NC}"
echo -e "${YELLOW}   https://www.ucusatolyesi.com.tr/api/setup${NC}"
echo -e "${YELLOW}   Login: <EMAIL> / admin123${NC}"

echo -e "${GREEN}🎉 Deployment tamamlandı!${NC}"
