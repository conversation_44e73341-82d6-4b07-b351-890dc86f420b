import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Blog from "@/lib/models/blog"
import { handleApiError } from "@/lib/api-utils"
import { checkAdminAuth } from "@/lib/auth-utils"

export async function GET(request: Request) {
  try {
    // Admin auth kontrolü
    const authCheck = await checkAdminAuth()
    if (!authCheck.authorized) {
      return NextResponse.json({ success: false, error: authCheck.error }, { status: 401 })
    }

    await dbConnect()

    const blogs = await Blog.find({})
      .select('title slug excerpt author category tags readTime likes isPublished publishedAt createdAt featuredImage comments')
      .sort({ createdAt: -1 })

    return NextResponse.json({ success: true, data: blogs })
  } catch (error) {
    console.error("Error fetching blogs:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function POST(request: Request) {
  try {
    // Admin auth kontrolü
    const authCheck = await checkAdminAuth()
    if (!authCheck.authorized) {
      return NextResponse.json({ success: false, error: authCheck.error }, { status: 401 })
    }

    const body = await request.json()
    await dbConnect()

    // Slug oluştur
    if (!body.slug && body.title) {
      body.slug = body.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()
    }

    // Okuma süresini hesapla (yaklaşık)
    if (body.content && !body.readTime) {
      const wordCount = body.content.split(' ').length
      body.readTime = Math.max(1, Math.ceil(wordCount / 200)) // 200 kelime/dakika
    }

    // publishedAt alanını set et
    if (body.isPublished && !body.publishedAt) {
      body.publishedAt = new Date()
    }

    const blog = await Blog.create(body)

    return NextResponse.json({ success: true, data: blog }, { status: 201 })
  } catch (error) {
    console.error("Error creating blog:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
