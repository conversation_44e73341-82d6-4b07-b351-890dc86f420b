import mongoose from "mongoose"

// Define the schema
const testimonialSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Ad Soyad gereklidir"],
      trim: true,
    },
    role: {
      type: String,
      trim: true,
    },
    quote: {
      type: String,
      required: [true, "Yorum gereklidir"],
      trim: true,
    },
    image: {
      type: String,
      trim: true,
    },
    rating: {
      type: Number,
      min: 1,
      max: 5,
      default: 5,
    },
    program: {
      type: String,
    },
    status: {
      type: String,
      enum: ["pending", "approved", "rejected"],
      default: "pending",
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  },
)

// Check if the model already exists to prevent overwriting
export default mongoose.models.Testimonial || mongoose.model("Testimonial", testimonialSchema)
