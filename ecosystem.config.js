module.exports = {
  apps: [
    {
      name: 'ucus-atolyesi',
      script: 'npm',
      args: 'start',
      cwd: '/root/ucus-atolyesi', // Sunucudaki gerçek path'i yazın
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        MONGODB_URI: '******************************************************************',
        NEXTAUTH_SECRET: 'd055581db8e08ea65c6ba9da9958f9fb1146796bbb3088a7bedb83f7ba7d4673',
        NEXTAUTH_URL: 'https://www.ucusatolyesi.com.tr',
        ENABLE_API_DOCS: 'false',
        ENABLE_SECURITY_LOGS: 'true',
        ENABLE_RATE_LIMIT: 'true'
      },
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      time: true
    }
  ]
}
