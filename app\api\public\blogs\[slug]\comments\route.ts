import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Blog from "@/lib/models/blog"

export async function GET(request: Request, { params }: { params: Promise<{ slug: string }> }) {
  try {
    await dbConnect()

    const { slug } = await params
    const blog = await Blog.findOne({
      slug: slug,
      isPublished: true,
      isActive: true
    }).select('comments')

    if (!blog) {
      return NextResponse.json({ success: false, error: "Blog bulunamadı" }, { status: 404 })
    }

    // Sadece onaylanmış yorumları döndür
    const approvedComments = blog.comments
      .filter(comment => comment.isApproved)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())

    return NextResponse.json({ success: true, data: approvedComments })
  } catch (error) {
    console.error("Error fetching blog comments:", error)
    return NextResponse.json({ success: false, error: "Yorumlar yüklenirken hata oluştu" }, { status: 500 })
  }
}

export async function POST(request: Request, { params }: { params: Promise<{ slug: string }> }) {
  try {
    await dbConnect()

    const { slug } = await params
    const body = await request.json()
    const { name, email, comment } = body

    if (!name || !email || !comment) {
      return NextResponse.json({
        success: false,
        error: "İsim, e-posta ve yorum alanları gereklidir"
      }, { status: 400 })
    }

    const blog = await Blog.findOne({
      slug: slug,
      isPublished: true,
      isActive: true
    })

    if (!blog) {
      return NextResponse.json({ success: false, error: "Blog bulunamadı" }, { status: 404 })
    }

    // Yeni yorum ekle
    blog.comments.push({
      name: name.trim(),
      email: email.trim().toLowerCase(),
      comment: comment.trim(),
      isApproved: false // Admin onayı gerekiyor
    })

    await blog.save()

    return NextResponse.json({
      success: true,
      message: "Yorumunuz başarıyla gönderildi. Onaylandıktan sonra görünecektir."
    })
  } catch (error) {
    console.error("Error adding blog comment:", error)
    return NextResponse.json({ success: false, error: "Yorum eklenirken hata oluştu" }, { status: 500 })
  }
}
