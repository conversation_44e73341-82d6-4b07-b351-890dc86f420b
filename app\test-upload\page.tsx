"use client"

import { useState } from "react"

export default function TestUploadPage() {
  const [file, setFile] = useState(null)
  const [uploading, setUploading] = useState(false)
  const [result, setResult] = useState(null)

  const handleFileChange = (e) => {
    setFile(e.target.files[0])
  }

  const handleUpload = async () => {
    if (!file) return

    setUploading(true)
    setResult(null)

    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', 'gallery')

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({ success: false, error: error.message })
    } finally {
      setUploading(false)
    }
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Upload Test</h1>
      
      <div className="space-y-4">
        <input
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          className="block"
        />
        
        <button
          onClick={handleUpload}
          disabled={!file || uploading}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          {uploading ? 'Uploading...' : 'Upload'}
        </button>
        
        {result && (
          <div className="mt-4 p-4 border rounded">
            <h3 className="font-bold">Result:</h3>
            <pre className="text-sm">{JSON.stringify(result, null, 2)}</pre>
            
            {result.success && result.url && (
              <div className="mt-4">
                <h4 className="font-bold">Uploaded Image:</h4>
                <img 
                  src={result.url} 
                  alt="Uploaded" 
                  className="max-w-xs mt-2"
                  onError={(e) => {
                    console.error("Image load error:", e)
                    e.target.style.border = "2px solid red"
                  }}
                  onLoad={() => {
                    console.log("Image loaded successfully:", result.url)
                  }}
                />
                <p className="text-sm text-gray-600 mt-2">URL: {result.url}</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
