import { NextResponse } from 'next/server'
import swaggerSpec from '@/lib/swagger'
import { checkAdminAuth } from '@/lib/auth-utils'

export async function GET() {
  try {
    // Auth kontrolü
    if (process.env.NODE_ENV !== "development") {
      const authCheck = await checkAdminAuth()
      console.log("Swagger JSON - Auth check:", authCheck)
      if (!authCheck.authorized) {
        return NextResponse.json(
          { error: 'Bu API dokümantasyonuna erişim yetkiniz yok' },
          { status: 403 }
        )
      }
    }

    return NextResponse.json(swaggerSpec)
  } catch (error) {
    console.error('Error serving Swagger spec:', error)
    return NextResponse.json(
      { error: 'API dokümantasyonu yüklenemedi' },
      { status: 500 }
    )
  }
}
