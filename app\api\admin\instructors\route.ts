import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Instructor from "@/lib/models/instructor"
import { handleApiError } from "@/lib/api-utils"

export async function GET(request: Request) {
  try {
    await dbConnect()

    const instructors = await Instructor.find({}).sort({ createdAt: -1 })

    return NextResponse.json({ success: true, data: instructors })
  } catch (error) {
    console.error("Error fetching instructors:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()

    await dbConnect()

    const instructor = await Instructor.create(body)

    return NextResponse.json({ success: true, data: instructor }, { status: 201 })
  } catch (error) {
    console.error("Error creating instructor:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
