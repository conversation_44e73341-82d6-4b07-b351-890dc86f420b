import { NextResponse } from "next/server"
import { extractYouTubeVideoId, getYouTubeVideoInfo, isValidYouTubeUrl } from "@/lib/youtube-utils"

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { videoUrl } = body

    if (!videoUrl) {
      return NextResponse.json(
        { success: false, error: "Video URL gereklidir" },
        { status: 400 }
      )
    }

    // YouTube URL'sini doğrula
    if (!isValidYouTubeUrl(videoUrl)) {
      return NextResponse.json(
        { success: false, error: "Geçersiz YouTube URL'si" },
        { status: 400 }
      )
    }

    // Video ID'sini çıkar
    const videoId = extractYouTubeVideoId(videoUrl)
    if (!videoId) {
      return NextResponse.json(
        { success: false, error: "YouTube video ID'si çıkarılamadı" },
        { status: 400 }
      )
    }

    try {
      // YouTube'dan video bilgilerini çek
      const videoInfo = await getYouTubeVideoInfo(videoId)
      
      return NextResponse.json({ success: true, data: videoInfo })
    } catch (error) {
      console.error("YouTube video info error:", error)
      return NextResponse.json(
        { success: false, error: "YouTube video bilgileri alınamadı. Video URL'sini kontrol edin." },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error("Error processing YouTube info request:", error)
    return NextResponse.json(
      { success: false, error: "İstek işlenirken hata oluştu" },
      { status: 500 }
    )
  }
}
