"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON>er, use<PERSON>ara<PERSON> } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { toast } from "sonner"
import { ArrowLeft, Save, Eye, MessageCircle, Check, X, Trash } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON>ertD<PERSON>og<PERSON>eader,
  AlertDialog<PERSON>it<PERSON>,
} from "@/components/ui/alert-dialog"

export default function EditBlogPage() {
  const router = useRouter()
  const params = useParams()
  const blogId = params.id as string

  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingBlog, setIsLoadingBlog] = useState(true)
  const [blog, setBlog] = useState(null)
  const [comments, setComments] = useState([])
  const [commentToDelete, setCommentToDelete] = useState(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  const [formData, setFormData] = useState({
    title: "",
    slug: "",
    excerpt: "",
    content: "",
    featuredImage: "",
    author: "Uçuş Atölyesi",
    category: "haberler",
    tags: "",
    isPublished: false,
  })

  const categories = [
    { value: "eğitim", label: "Eğitim" },
    { value: "teknoloji", label: "Teknoloji" },
    { value: "etkinlik", label: "Etkinlik" },
    { value: "haberler", label: "Haberler" },
    { value: "ipuçları", label: "İpuçları" },
  ]

  useEffect(() => {
    const fetchBlog = async () => {
      try {
        const response = await fetch(`/api/admin/blogs/${blogId}`)
        const data = await response.json()

        if (data.success) {
          const blogData = data.data
          setBlog(blogData)
          setComments(blogData.comments || [])
          setFormData({
            title: blogData.title || "",
            slug: blogData.slug || "",
            excerpt: blogData.excerpt || "",
            content: blogData.content || "",
            featuredImage: blogData.featuredImage || "",
            author: blogData.author || "Uçuş Atölyesi",
            category: blogData.category || "haberler",
            tags: blogData.tags ? blogData.tags.join(", ") : "",
            isPublished: blogData.isPublished || false,
          })
        } else {
          toast.error("Blog yüklenirken hata oluştu")
          router.push("/admin/blogs")
        }
      } catch (error) {
        console.error("Error fetching blog:", error)
        toast.error("Blog yüklenirken hata oluştu")
        router.push("/admin/blogs")
      } finally {
        setIsLoadingBlog(false)
      }
    }

    fetchBlog()
  }, [blogId, router])

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
      // Auto-generate slug from title
      ...(name === "title" && {
        slug: value
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, "")
          .replace(/\s+/g, "-")
          .replace(/-+/g, "-")
          .trim(),
      }),
    }))
  }

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSwitchChange = (name, checked) => {
    setFormData((prev) => ({ ...prev, [name]: checked }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const response = await fetch(`/api/admin/blogs/${blogId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          tags: formData.tags.split(",").map(tag => tag.trim()).filter(tag => tag),
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast.success("Blog yazısı başarıyla güncellendi.")
        router.push("/admin/blogs")
      } else {
        toast.error(data.error || "Blog yazısı güncellenirken bir hata oluştu.")
      }
    } catch (error) {
      console.error("Error updating blog:", error)
      toast.error("Blog yazısı güncellenirken bir hata oluştu.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleCommentApproval = async (commentId, isApproved) => {
    try {
      const response = await fetch(`/api/admin/blogs/${blogId}/comments/${commentId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ isApproved }),
      })

      const data = await response.json()

      if (data.success) {
        setComments(prev =>
          prev.map(comment =>
            comment._id === commentId
              ? { ...comment, isApproved }
              : comment
          )
        )
        toast.success(isApproved ? "Yorum onaylandı" : "Yorum onayı kaldırıldı")
      } else {
        toast.error("Yorum durumu güncellenirken hata oluştu")
      }
    } catch (error) {
      console.error("Error updating comment:", error)
      toast.error("Yorum durumu güncellenirken hata oluştu")
    }
  }

  const handleDeleteComment = async () => {
    try {
      const response = await fetch(`/api/admin/blogs/${blogId}/comments/${commentToDelete._id}`, {
        method: "DELETE",
      })

      const data = await response.json()

      if (data.success) {
        setComments(prev => prev.filter(comment => comment._id !== commentToDelete._id))
        toast.success("Yorum silindi")
        setIsDeleteDialogOpen(false)
        setCommentToDelete(null)
      } else {
        toast.error("Yorum silinirken hata oluştu")
      }
    } catch (error) {
      console.error("Error deleting comment:", error)
      toast.error("Yorum silinirken hata oluştu")
    }
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("tr-TR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  if (isLoadingBlog) {
    return (
      <div className="container py-20">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-64 bg-gray-200 rounded mb-6"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div>
      <div className="flex items-center gap-4 mb-8">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/blogs">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Geri
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">Blog Yazısını Düzenle</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          {/* Blog Edit Form */}
          <Card>
            <CardHeader>
              <CardTitle>Blog İçeriği</CardTitle>
              <CardDescription>Blog yazısının içeriğini düzenleyin.</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="title">Başlık</Label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    placeholder="Blog yazısı başlığı"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="slug">URL Slug</Label>
                  <Input
                    id="slug"
                    name="slug"
                    value={formData.slug}
                    onChange={handleChange}
                    placeholder="blog-yazisi-url"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="excerpt">Özet</Label>
                  <Textarea
                    id="excerpt"
                    name="excerpt"
                    value={formData.excerpt}
                    onChange={handleChange}
                    placeholder="Blog yazısının kısa özeti..."
                    rows={3}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="content">İçerik</Label>
                  <Textarea
                    id="content"
                    name="content"
                    value={formData.content}
                    onChange={handleChange}
                    placeholder="Blog yazısının tam içeriği..."
                    rows={15}
                    required
                  />
                </div>

                <div className="flex gap-4">
                  <Button type="submit" disabled={isLoading} className="bg-sky-500 hover:bg-sky-600">
                    <Save className="h-4 w-4 mr-2" />
                    {isLoading ? "Kaydediliyor..." : "Güncelle"}
                  </Button>
                  <Button type="button" variant="outline" asChild>
                    <Link href="/admin/blogs">İptal</Link>
                  </Button>
                  {blog && (
                    <Button type="button" variant="outline" asChild>
                      <Link href={`/blog/${blog.slug}`} target="_blank">
                        <Eye className="h-4 w-4 mr-2" />
                        Önizle
                      </Link>
                    </Button>
                  )}
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          {/* Blog Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Yayın Ayarları</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="isPublished"
                  checked={formData.isPublished}
                  onCheckedChange={(checked) => handleSwitchChange("isPublished", checked)}
                />
                <Label htmlFor="isPublished">Yayında</Label>
              </div>
            </CardContent>
          </Card>

          {/* Blog Details */}
          <Card>
            <CardHeader>
              <CardTitle>Blog Detayları</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="author">Yazar</Label>
                <Input
                  id="author"
                  name="author"
                  value={formData.author}
                  onChange={handleChange}
                  placeholder="Yazar adı"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Kategori</Label>
                <Select value={formData.category} onValueChange={(value) => handleSelectChange("category", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Kategori seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tags">Etiketler</Label>
                <Input
                  id="tags"
                  name="tags"
                  value={formData.tags}
                  onChange={handleChange}
                  placeholder="etiket1, etiket2, etiket3"
                />
                <p className="text-sm text-gray-500">Etiketleri virgülle ayırın</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="featuredImage">Kapak Görseli URL</Label>
                <Input
                  id="featuredImage"
                  name="featuredImage"
                  value={formData.featuredImage}
                  onChange={handleChange}
                  placeholder="/blog-image.jpg"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Comments Management */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              Yorumlar ({comments.length})
            </CardTitle>
            <CardDescription>
              Blog yazısına yapılan yorumları yönetin.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {comments.length === 0 ? (
              <div className="text-center py-8">
                <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Henüz yorum yapılmamış.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {comments.map((comment, index) => (
                  <div key={comment._id || index} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-semibold">{comment.name}</h4>
                          <Badge variant={comment.isApproved ? "default" : "secondary"}>
                            {comment.isApproved ? "Onaylandı" : "Beklemede"}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            {formatDate(comment.createdAt)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{comment.email}</p>
                        <p className="text-gray-700">{comment.comment}</p>
                      </div>
                      <div className="flex gap-2 ml-4">
                        {!comment.isApproved ? (
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-green-600 hover:text-green-700 hover:bg-green-50"
                            onClick={() => handleCommentApproval(comment._id, true)}
                          >
                            <Check className="h-4 w-4" />
                          </Button>
                        ) : (
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                            onClick={() => handleCommentApproval(comment._id, false)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          onClick={() => {
                            setCommentToDelete(comment)
                            setIsDeleteDialogOpen(true)
                          }}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Delete Comment Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Yorumu Silmek İstediğinize Emin misiniz?</AlertDialogTitle>
            <AlertDialogDescription>
              Bu işlem geri alınamaz. Yorum kalıcı olarak silinecektir.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction className="bg-red-500 hover:bg-red-600" onClick={handleDeleteComment}>
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
