"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

export function EnrollmentForm() {
  const router = useRouter()
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    childName: "",
    childAge: "",
    parentName: "",
    email: "",
    phone: "",
    program: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Simulate API call if in development mode without MongoDB
      if (process.env.NODE_ENV === "development" && !process.env.MONGODB_URI) {
        setTimeout(() => {
          toast({
            title: "Ön Kayıt Alındı (Geliştirme Modu)",
            description: "Detaylı kayıt için sizinle iletişime geçeceğiz.",
          })
          router.push("/register")
          setIsSubmitting(false)
        }, 1500)
        return
      }

      // Real API call
      const response = await fetch("/api/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          address: "Ön kayıt formu üzerinden gönderildi.",
          notes: "Detaylı bilgi için iletişime geçilecek.",
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Ön Kayıt Alındı",
          description: "Detaylı kayıt için sizinle iletişime geçeceğiz.",
        })
        router.push("/register")
      } else {
        toast({
          title: "Hata",
          description: data.error || "Kayıt işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: "Kayıt işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Hızlı Ön Kayıt</CardTitle>
        <CardDescription>Eğitimlerimize katılmak için ön kayıt formunu doldurun.</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="childName">Çocuğun Adı Soyadı</Label>
            <Input
              id="childName"
              name="childName"
              value={formData.childName}
              onChange={handleChange}
              placeholder="Çocuğun adı soyadı"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="childAge">Çocuğun Yaşı</Label>
            <Input
              id="childAge"
              name="childAge"
              type="number"
              min="6"
              max="14"
              value={formData.childAge}
              onChange={handleChange}
              placeholder="Çocuğun yaşı"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="parentName">Veli Adı Soyadı</Label>
            <Input
              id="parentName"
              name="parentName"
              value={formData.parentName}
              onChange={handleChange}
              placeholder="Veli adı soyadı"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">E-posta</Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="<EMAIL>"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">Telefon</Label>
            <Input
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              placeholder="05XX XXX XX XX"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="program">Eğitim Programı</Label>
            <Select value={formData.program} onValueChange={(value) => handleSelectChange("program", value)} required>
              <SelectTrigger id="program">
                <SelectValue placeholder="Program seçin" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ua1">UA1 - Giriş Seviyesi (6-8 yaş)</SelectItem>
                <SelectItem value="ua2">UA2 - Orta Seviye (9-11 yaş)</SelectItem>
                <SelectItem value="ua3">UA3 - İleri Seviye (12-14 yaş)</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button type="submit" className="w-full bg-sky-500 hover:bg-sky-600" disabled={isSubmitting}>
            {isSubmitting ? "Gönderiliyor..." : "Ön Kayıt Oluştur"}
          </Button>
        </form>
      </CardContent>
      <CardFooter className="text-xs text-gray-500 dark:text-gray-400 text-center">
        Bilgileriniz gizlilik politikamıza uygun olarak saklanacaktır.
      </CardFooter>
    </Card>
  )
}
