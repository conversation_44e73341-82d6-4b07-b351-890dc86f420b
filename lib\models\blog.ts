import mongoose from "mongoose"

// Blog Comment Schema
const commentSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "İsim gereklidir"],
      trim: true,
    },
    email: {
      type: String,
      required: [true, "E-posta gereklidir"],
      trim: true,
    },
    comment: {
      type: String,
      required: [true, "Yorum gereklidir"],
      trim: true,
    },
    isApproved: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  }
)

// Blog Schema
const blogSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Blog başlığı gereklidir"],
      trim: true,
    },
    slug: {
      type: String,
      required: [true, "Blog slug'ı gereklidir"],
      trim: true,
      unique: true,
    },
    excerpt: {
      type: String,
      required: [true, "Blog özeti gereklidir"],
      trim: true,
    },
    content: {
      type: String,
      required: [true, "Blog içeriği gereklidir"],
    },
    featuredImage: {
      type: String,
      trim: true,
    },
    author: {
      type: String,
      required: [true, "Yazar gereklidir"],
      trim: true,
      default: "Uçuş Atölyesi",
    },
    category: {
      type: String,
      trim: true,
      enum: ["eğitim", "teknoloji", "etkinlik", "haberler", "ipuçları"],
      default: "haberler",
    },
    tags: {
      type: [String],
      default: [],
    },
    readTime: {
      type: Number,
      default: 5, // dakika
    },
    likes: {
      type: Number,
      default: 0,
    },
    likedBy: {
      type: [String], // IP adresleri veya user ID'leri
      default: [],
    },
    comments: [commentSchema],
    isPublished: {
      type: Boolean,
      default: false,
    },
    publishedAt: {
      type: Date,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
)

// Middleware to set publishedAt when isPublished becomes true
blogSchema.pre('save', function(next) {
  if (this.isPublished && !this.publishedAt) {
    this.publishedAt = new Date()
  }
  next()
})

// Check if the model already exists to prevent overwriting
export default mongoose.models.Blog || mongoose.model("Blog", blogSchema)
