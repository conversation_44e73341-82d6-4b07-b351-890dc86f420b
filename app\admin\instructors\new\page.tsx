"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/components/ui/use-toast"
import { ArrowLeft, Save, Camera, Upload, Loader2 } from "lucide-react"
import { ImageCropper } from "@/components/image-cropper"

export default function NewInstructorPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState("")
  const [isUploading, setIsUploading] = useState(false)
  const [showCropper, setShowCropper] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    role: "",
    email: "",
    phone: "",
    expertise: "",
    experience: "",
    bio: "",
    image: "",
    isActive: true,
  })

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSwitchChange = (name, checked) => {
    setFormData((prev) => ({ ...prev, [name]: checked }))
  }

  const handleFileChange = (e) => {
    const file = e.target.files?.[0]
    if (file) {
      // Dosya boyutu kontrolü (5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "Hata",
          description: "Dosya boyutu 5MB'dan küçük olmalıdır.",
          variant: "destructive",
        })
        return
      }

      // Dosya tipi kontrolü
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Hata",
          description: "Lütfen geçerli bir resim dosyası seçin.",
          variant: "destructive",
        })
        return
      }

      setSelectedFile(file)
      setShowCropper(true)
    }
  }

  const handleCropComplete = async (croppedFile: File) => {
    setIsUploading(true)
    setShowCropper(false)

    try {
      const formData = new FormData()
      formData.append('file', croppedFile)
      formData.append('type', 'instructor')

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      const data = await response.json()

      if (data.success) {
        setPreviewUrl(data.url)
        setFormData(prev => ({ ...prev, image: data.url }))
        toast({
          title: "Başarılı",
          description: "Fotoğraf başarıyla yüklendi.",
        })
      } else {
        throw new Error(data.error || 'Upload failed')
      }
    } catch (error) {
      console.error('Upload error:', error)
      toast({
        title: "Hata",
        description: "Fotoğraf yüklenirken bir hata oluştu.",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
      setSelectedFile(null)
    }
  }

  const handleCropCancel = () => {
    setShowCropper(false)
    setSelectedFile(null)
  }



  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Resim yüklenmemişse uyarı ver
      if (!formData.image) {
        toast({
          title: "Hata",
          description: "Lütfen eğitmen fotoğrafı yükleyin.",
          variant: "destructive",
        })
        setIsLoading(false)
        return
      }

      const response = await fetch("/api/admin/instructors", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Eğitmen Oluşturuldu",
          description: "Yeni eğitmen başarıyla oluşturuldu.",
        })
        router.push("/admin/instructors")
      } else {
        toast({
          title: "Hata",
          description: data.error || "Eğitmen oluşturulurken bir hata oluştu.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error creating instructor:", error)
      toast({
        title: "Hata",
        description: "Eğitmen oluşturulurken bir hata oluştu.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
      setIsUploading(false)
    }
  }

  return (
    <div>
      <div className="flex items-center gap-4 mb-8">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/instructors">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Geri
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">Yeni Eğitmen</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Eğitmen Bilgileri</CardTitle>
          <CardDescription>Yeni bir eğitmen oluşturun.</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">Ad Soyad</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Dr. Ahmet Kaya"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="role">Rol/Unvan</Label>
                <Input
                  id="role"
                  name="role"
                  value={formData.role}
                  onChange={handleChange}
                  placeholder="Baş Eğitmen"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="email">E-posta</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Telefon</Label>
                <Input
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder="05551234567"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="expertise">Uzmanlık Alanları</Label>
                <Input
                  id="expertise"
                  name="expertise"
                  value={formData.expertise}
                  onChange={handleChange}
                  placeholder="Drone Teknolojisi, Havacılık Mühendisliği"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="experience">Deneyim</Label>
                <Input
                  id="experience"
                  name="experience"
                  value={formData.experience}
                  onChange={handleChange}
                  placeholder="8 yıl"
                />
              </div>
            </div>



            <div className="space-y-2">
              <Label htmlFor="bio">Biyografi</Label>
              <Textarea
                id="bio"
                name="bio"
                value={formData.bio}
                onChange={handleChange}
                placeholder="Eğitmen hakkında detaylı bilgi..."
                rows={4}
                required
              />
            </div>

            {/* Fotoğraf Yükleme */}
            <div className="space-y-4">
              <Label>Eğitmen Fotoğrafı</Label>

              {/* Dosya Seçici */}
              <div className="flex flex-col items-center space-y-4 p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-sky-400 transition-colors">
                {previewUrl ? (
                  <div className="relative">
                    <img
                      src={previewUrl}
                      alt="Preview"
                      className="w-32 h-32 object-cover rounded-full border-4 border-white shadow-lg"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="absolute top-0 right-0"
                      onClick={() => {
                        setSelectedFile(null)
                        setPreviewUrl("")
                      }}
                    >
                      Değiştir
                    </Button>
                  </div>
                ) : (
                  <div className="text-center">
                    <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-lg font-medium text-gray-700 mb-2">Eğitmen Fotoğrafı Yükle</p>
                    <p className="text-sm text-gray-500 mb-4">
                      JPG, PNG veya GIF formatında, maksimum 5MB
                    </p>
                  </div>
                )}

                <Label htmlFor="imageFile" className="cursor-pointer">
                  <div className="flex items-center gap-2 px-4 py-2 bg-sky-50 hover:bg-sky-100 rounded-lg border border-sky-200 transition-colors">
                    <Camera className="h-4 w-4 text-sky-600" />
                    <span className="text-sm text-sky-600">
                      {previewUrl ? "Farklı Fotoğraf Seç" : "Fotoğraf Seç"}
                    </span>
                  </div>
                </Label>
                <Input
                  id="imageFile"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="hidden"
                />
              </div>

              {isUploading && (
                <div className="flex items-center justify-center gap-2 text-sky-600">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm">Fotoğraf yükleniyor...</span>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => handleSwitchChange("isActive", checked)}
              />
              <Label htmlFor="isActive">Aktif</Label>
            </div>

            <div className="flex gap-4">
              <Button type="submit" disabled={isLoading} className="bg-sky-500 hover:bg-sky-600">
                <Save className="h-4 w-4 mr-2" />
                {isLoading ? "Kaydediliyor..." : "Kaydet"}
              </Button>
              <Button type="button" variant="outline" asChild>
                <Link href="/admin/instructors">İptal</Link>
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Image Cropper Modal */}
      {showCropper && selectedFile && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
            <ImageCropper
              imageFile={selectedFile}
              onCropComplete={handleCropComplete}
              onCancel={handleCropCancel}
              aspectRatio={1} // Square crop for instructor photos
            />
          </div>
        </div>
      )}
    </div>
  )
}
