import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Testimonial from "@/lib/models/testimonial"

export async function GET() {
  try {
    await dbConnect()

    const testimonials = await Testimonial.find({ isActive: true })
      .select('name role quote image rating program')
      .sort({ createdAt: -1 })

    return NextResponse.json({ success: true, data: testimonials })
  } catch (error) {
    console.error("Error fetching public testimonials:", error)
    return NextResponse.json({ success: false, error: "Yorumlar yüklenirken hata olu<PERSON>tu" }, { status: 500 })
  }
}
