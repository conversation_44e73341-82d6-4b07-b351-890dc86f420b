import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Gallery from "@/lib/models/gallery"

export async function GET() {
  try {
    await dbConnect()

    const gallery = await Gallery.find({ isActive: true })
      .select('title description image category type videoUrl videoId videoTitle videoDescription videoThumbnail videoDuration')
      .sort({ createdAt: -1 })

    return NextResponse.json({ success: true, data: gallery })
  } catch (error) {
    console.error("Error fetching public gallery:", error)
    return NextResponse.json({ success: false, error: "<PERSON><PERSON> yükle<PERSON> hata olu<PERSON>" }, { status: 500 })
  }
}
