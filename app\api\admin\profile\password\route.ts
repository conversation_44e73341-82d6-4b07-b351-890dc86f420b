import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import User from "@/lib/models/user"
import { checkAdminAuth } from "@/lib/auth-utils"
import { handleApiError } from "@/lib/api-utils"

export async function PUT(request: Request) {
  try {
    // Admin auth kontrolü
    const authCheck = await checkAdminAuth()
    if (!authCheck.authorized) {
      return NextResponse.json({ success: false, error: authCheck.error }, { status: 401 })
    }

    const body = await request.json()
    const { currentPassword, newPassword } = body

    if (!currentPassword || !newPassword) {
      return NextResponse.json({
        success: false,
        error: "Mevcut şifre ve yeni şifre gereklidir"
      }, { status: 400 })
    }

    if (newPassword.length < 6) {
      return NextResponse.json({
        success: false,
        error: "Yeni şifre en az 6 karakter olmalıdır"
      }, { status: 400 })
    }

    // Development modunda mock response döndür
    if (process.env.NODE_ENV === "development") {
      return NextResponse.json({
        success: true,
        message: "Şifre başarıyla güncellendi (Development Mode)"
      })
    }

    await dbConnect()

    const user = await User.findById(authCheck.user.id)

    if (!user) {
      return NextResponse.json({ success: false, error: "Kullanıcı bulunamadı" }, { status: 404 })
    }

    // Mevcut şifreyi kontrol et
    const isCurrentPasswordValid = await user.comparePassword(currentPassword)

    if (!isCurrentPasswordValid) {
      return NextResponse.json({
        success: false,
        error: "Mevcut şifre hatalı"
      }, { status: 400 })
    }

    // Yeni şifreyi hashle ve kaydet
    user.password = newPassword // Pre-save hook otomatik olarak hashleyecek
    user.updatedAt = new Date()
    await user.save()

    return NextResponse.json({
      success: true,
      message: "Şifre başarıyla güncellendi"
    })
  } catch (error) {
    console.error("Error updating password:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
