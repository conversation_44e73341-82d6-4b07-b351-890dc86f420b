"use client"

import { useState } from "react"
import Image from "next/image"
import { getPlaceholderImage } from "@/lib/placeholder-images"

interface ImageWithFallbackProps {
  src: string
  alt: string
  width?: number
  height?: number
  fill?: boolean
  className?: string
  type?: string
}

export function ImageWithFallback({
  src,
  alt,
  width,
  height,
  fill = false,
  className = "",
  type = "default",
}: ImageWithFallbackProps) {
  const [error, setError] = useState(false)

  const handleError = () => {
    setError(true)
  }

  const fallbackSrc = getPlaceholderImage(type)

  return (
    <Image
      src={error ? fallbackSrc : src}
      alt={alt}
      width={!fill ? width : undefined}
      height={!fill ? height : undefined}
      fill={fill}
      className={className}
      onError={handleError}
    />
  )
}
