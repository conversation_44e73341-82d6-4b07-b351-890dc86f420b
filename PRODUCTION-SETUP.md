# Production Deployment Rehberi - www.ucusatolyesi.com.tr

## 1. Environment Değişkenleri

Sunucunuzda aşağıdaki environment değişkenlerini ayarlayın:

```bash
# MongoDB bağlantısı
MONGODB_URI=******************************************************************

# NextAuth Configuration
NEXTAUTH_SECRET=d055581db8e08ea65c6ba9da9958f9fb1146796bbb3088a7bedb83f7ba7d4673
NEXTAUTH_URL=https://www.ucusatolyesi.com.tr

# Node Environment
NODE_ENV=production

# Security Settings
ENABLE_API_DOCS=false
ENABLE_SECURITY_LOGS=true
ENABLE_RATE_LIMIT=true
```

## 2. İlk Kurulum Adımları

### Adım 1: <PERSON><PERSON> Oluşturma
Sunucuda site yayınlandıktan sonra:

```
https://www.ucusatolyesi.com.tr/api/setup
```

Bu URL'yi ziyaret ederek admin kullanı<PERSON>ını oluşturun.

### Adım 2: Login Bilgileri
```
Email: <EMAIL>
Password: admin123
```

## 3. Sorun Giderme

### Login Sorunu
Eğer login olamıyorsanız:

1. **Environment değişkenlerini kontrol edin:**
   - `MONGODB_URI` doğru mu?
   - `NEXTAUTH_SECRET` ayarlandı mı?
   - `NEXTAUTH_URL` doğru domain'i gösteriyor mu?

2. **Veritabanı bağlantısını test edin:**
   ```
   https://www.ucusatolyesi.com.tr/api/setup
   ```

3. **Browser console'u kontrol edin:**
   - F12 tuşuna basın
   - Console sekmesinde hata mesajları var mı?

### Logo Sorunu
Logo görünmüyorsa:

1. `/public/logo.jpg` dosyasının var olduğundan emin olun
2. Admin panelinden Site Ayarları > Logo bölümünden yeni logo yükleyin

## 4. Güvenlik Notları

- Production'da `admin123` şifresini değiştirin
- `NEXTAUTH_SECRET` değerini güvenli tutun
- Gerekirse `ADMIN_IP_WHITELIST` kullanın

## 5. Deployment Seçenekleri

### A) PM2 ile Deployment
```bash
# Uygulamayı build edin
npm run build

# PM2 ile başlatın
pm2 start ecosystem.config.js

# PM2'yi sistem başlangıcında çalıştırın
pm2 startup
pm2 save
```

### B) Docker ile Deployment
```bash
# Docker image'ı build edin
docker build -t ucus-atolyesi .

# Docker Compose ile çalıştırın
docker-compose up -d
```

### C) Systemd ile Deployment
```bash
# Systemd service dosyası oluşturun
sudo nano /etc/systemd/system/ucus-atolyesi.service

# Service'i etkinleştirin
sudo systemctl enable ucus-atolyesi
sudo systemctl start ucus-atolyesi
```

## 6. SSL Sertifikası (Let's Encrypt)

```bash
# Certbot kurulumu
sudo apt install certbot python3-certbot-nginx

# SSL sertifikası alın
sudo certbot --nginx -d www.ucusatolyesi.com.tr -d ucusatolyesi.com.tr

# Otomatik yenileme
sudo crontab -e
# Şu satırı ekleyin:
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 7. Monitoring

Sunucu loglarını kontrol etmek için:
```bash
# PM2 kullanıyorsanız
pm2 logs ucus-atolyesi
pm2 monit

# Docker kullanıyorsanız
docker logs ucus-atolyesi
docker-compose logs -f

# Systemd kullanıyorsanız
journalctl -u ucus-atolyesi -f

# Health check
curl https://www.ucusatolyesi.com.tr/api/health
```

## 8. Backup Stratejisi

```bash
# MongoDB backup
mongodump --uri="******************************************************************" --out=/backup/$(date +%Y%m%d)

# Uploads klasörü backup
tar -czf /backup/uploads-$(date +%Y%m%d).tar.gz /path/to/app/public/uploads
```
