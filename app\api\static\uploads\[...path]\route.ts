import { NextRequest, NextResponse } from "next/server"
import { readFile } from "fs/promises"
import { join } from "path"
import { existsSync } from "fs"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { path } = await params
    const filePath = join(process.cwd(), 'public', 'uploads', ...path)
    
    console.log("Static file request:", filePath)
    
    // Dosya var mı kontrol et
    if (!existsSync(filePath)) {
      console.log("File not found:", filePath)
      return new NextResponse("File not found", { status: 404 })
    }
    
    // Dosyayı oku
    const fileBuffer = await readFile(filePath)
    
    // MIME type'ı belirle
    const extension = path[path.length - 1].split('.').pop()?.toLowerCase()
    let contentType = 'application/octet-stream'
    
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        contentType = 'image/jpeg'
        break
      case 'png':
        contentType = 'image/png'
        break
      case 'gif':
        contentType = 'image/gif'
        break
      case 'webp':
        contentType = 'image/webp'
        break
      case 'svg':
        contentType = 'image/svg+xml'
        break
    }
    
    console.log("Serving file:", filePath, "Content-Type:", contentType)
    
    // Response headers
    const headers = new Headers()
    headers.set('Content-Type', contentType)
    headers.set('Cache-Control', 'public, max-age=31536000, immutable')
    headers.set('Content-Length', fileBuffer.length.toString())
    
    return new NextResponse(fileBuffer, {
      status: 200,
      headers
    })
    
  } catch (error) {
    console.error("Static file serving error:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}
