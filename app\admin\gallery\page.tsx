"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"
import { PlusCircle, Pencil, Trash, Eye, Play, Image as ImageIcon } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

export default function GalleryPage() {
  const [galleryItems, setGalleryItems] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [itemToDelete, setItemToDelete] = useState(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  const fetchGalleryItems = async () => {
    try {
      setIsLoading(true)
      const response = await fetch("/api/admin/gallery")
      const data = await response.json()

      if (data.success) {
        setGalleryItems(data.data)
      } else {
        console.error("Error fetching gallery items:", data.error)
        toast.error("Galeri öğeleri yüklenirken bir hata oluştu.")
      }
    } catch (error) {
      console.error("Error fetching gallery items:", error)
      toast.error("Galeri öğeleri yüklenirken bir hata oluştu.")
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchGalleryItems()
  }, [])

  const handleDeleteClick = (item) => {
    setItemToDelete(item)
    setIsDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    try {
      const response = await fetch(`/api/admin/gallery/${itemToDelete._id || itemToDelete.id}`, {
        method: "DELETE",
      })

      const data = await response.json()

      if (data.success) {
        setGalleryItems((prev) => prev.filter((item) => (item._id || item.id) !== (itemToDelete._id || itemToDelete.id)))
        toast.success(`"${itemToDelete.title}" başarıyla silindi.`)
        setIsDeleteDialogOpen(false)
        setItemToDelete(null)
      } else {
        toast.error(data.error || "Galeri öğesi silinirken bir hata oluştu.")
      }
    } catch (error) {
      console.error("Error deleting gallery item:", error)
      toast.error("Galeri öğesi silinirken bir hata oluştu.")
    }
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("tr-TR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }).format(date)
  }

  const getCategoryName = (category) => {
    switch (category) {
      case "ua1":
        return "UA1 - Giriş Seviyesi"
      case "ua2":
        return "UA2 - Orta Seviye"
      case "ua3":
        return "UA3 - İleri Seviye"
      case "workshop":
        return "Atölye Çalışması"
      case "teamwork":
        return "Takım Çalışması"
      case "graduation":
        return "Mezuniyet"
      default:
        return category
    }
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Galeri</h1>
        <div className="flex gap-2">
          <Button className="bg-sky-500 hover:bg-sky-600" asChild>
            <Link href="/admin/gallery/new?type=image">
              <ImageIcon className="h-4 w-4 mr-2" />
              Fotoğraf Ekle
            </Link>
          </Button>
          <Button className="bg-red-500 hover:bg-red-600" asChild>
            <Link href="/admin/gallery/new?type=video">
              <Play className="h-4 w-4 mr-2" />
              Video Ekle
            </Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Galeri Listesi</CardTitle>
          <CardDescription>Tüm galeri öğelerini görüntüleyin ve yönetin.</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <p>Yükleniyor...</p>
            </div>
          ) : galleryItems.length === 0 ? (
            <div className="text-center py-8">
              <p>Henüz galeri öğesi bulunmuyor.</p>
              <div className="flex gap-2 justify-center mt-4">
                <Button className="bg-sky-500 hover:bg-sky-600" asChild>
                  <Link href="/admin/gallery/new?type=image">
                    <ImageIcon className="h-4 w-4 mr-2" />
                    Fotoğraf Ekle
                  </Link>
                </Button>
                <Button className="bg-red-500 hover:bg-red-600" asChild>
                  <Link href="/admin/gallery/new?type=video">
                    <Play className="h-4 w-4 mr-2" />
                    Video Ekle
                  </Link>
                </Button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {galleryItems.map((item) => (
                <Card key={item._id || item.id} className="overflow-hidden">
                  <div className="relative h-48">
                    {item.type === "video" ? (
                      <div className="relative w-full h-full">
                        <Image
                          src={item.videoThumbnail || "/placeholder.jpg"}
                          alt={item.title}
                          fill
                          className="object-cover"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                          <div className="bg-red-600 rounded-full p-3">
                            <Play className="h-6 w-6 text-white fill-white" />
                          </div>
                        </div>
                        <div className="absolute top-2 left-2">
                          <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium">
                            VIDEO
                          </span>
                        </div>
                      </div>
                    ) : (
                      <div className="relative w-full h-full">
                        <Image
                          src={item.image || "/placeholder.jpg"}
                          alt={item.title}
                          fill
                          className="object-cover"
                        />
                        <div className="absolute top-2 left-2">
                          <span className="bg-sky-600 text-white px-2 py-1 rounded text-xs font-medium">
                            FOTOĞRAF
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold text-lg mb-2">
                      {item.title || (item.type === "video" ? item.videoTitle : "Başlıksız")}
                    </h3>
                    <p className="text-sm text-gray-600 mb-2">
                      {item.description || (item.type === "video" ? item.videoDescription : "")}
                    </p>
                    <div className="flex justify-between items-center text-xs text-gray-500">
                      <span className="bg-sky-100 text-sky-800 px-2 py-1 rounded">
                        {getCategoryName(item.category)}
                      </span>
                      <span>{formatDate(item.createdAt)}</span>
                    </div>
                  </CardContent>
                  <CardFooter className="p-4 pt-0">
                    <div className="flex justify-end gap-2 w-full">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/gallery`} target="_blank">
                          <Eye className="h-4 w-4" />
                          <span className="sr-only">Görüntüle</span>
                        </Link>
                      </Button>
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/admin/gallery/${item._id || item.id}/edit`}>
                          <Pencil className="h-4 w-4" />
                          <span className="sr-only">Düzenle</span>
                        </Link>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                        onClick={() => handleDeleteClick(item)}
                      >
                        <Trash className="h-4 w-4" />
                        <span className="sr-only">Sil</span>
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <p className="text-sm text-gray-500">Toplam {galleryItems.length} öğe</p>
        </CardFooter>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Galeri Öğesini Silmek İstediğinize Emin misiniz?</AlertDialogTitle>
            <AlertDialogDescription>
              Bu işlem geri alınamaz. Bu galeri öğesi kalıcı olarak silinecektir.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction className="bg-red-500 hover:bg-red-600" onClick={handleDeleteConfirm}>
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
