import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import User from "@/lib/models/user"

export async function GET() {
  try {
    console.log("=== AUTH DEBUG API ===")
    
    // Environment kontrol
    const envInfo = {
      NODE_ENV: process.env.NODE_ENV,
      NEXTAUTH_URL: process.env.NEXTAUTH_URL,
      NEXTAUTH_SECRET_EXISTS: !!process.env.NEXTAUTH_SECRET,
      NEXTAUTH_SECRET_LENGTH: process.env.NEXTAUTH_SECRET?.length || 0,
      MONGODB_URI_EXISTS: !!process.env.MONGODB_URI,
      MONGODB_URI_PREVIEW: process.env.MONGODB_URI?.substring(0, 30) + "..."
    }
    
    console.log("Environment Info:", envInfo)
    
    // Database bağlantı testi
    await dbConnect()
    console.log("Database connection: SUCCESS")
    
    // Admin kullanıcısı kontrol
    const adminUser = await User.findOne({ email: "<EMAIL>" })
    
    const userInfo = {
      userExists: !!adminUser,
      userName: adminUser?.name,
      userEmail: adminUser?.email,
      userRole: adminUser?.role,
      userActive: adminUser?.isActive,
      hasPassword: !!adminUser?.password,
      passwordLength: adminUser?.password?.length || 0
    }
    
    console.log("User Info:", userInfo)
    
    // Test password comparison
    let passwordTest = null
    if (adminUser) {
      try {
        passwordTest = await adminUser.comparePassword("admin123")
        console.log("Password test result:", passwordTest)
      } catch (error) {
        console.error("Password test error:", error)
        passwordTest = `ERROR: ${error.message}`
      }
    }
    
    return NextResponse.json({
      success: true,
      debug: {
        environment: envInfo,
        database: "connected",
        user: userInfo,
        passwordTest: passwordTest,
        timestamp: new Date().toISOString()
      }
    })
    
  } catch (error) {
    console.error("Debug API Error:", error)
    
    return NextResponse.json({
      success: false,
      error: error.message,
      debug: {
        environment: {
          NODE_ENV: process.env.NODE_ENV,
          NEXTAUTH_URL: process.env.NEXTAUTH_URL,
          NEXTAUTH_SECRET_EXISTS: !!process.env.NEXTAUTH_SECRET,
          MONGODB_URI_EXISTS: !!process.env.MONGODB_URI
        },
        timestamp: new Date().toISOString()
      }
    }, { status: 500 })
  }
}
