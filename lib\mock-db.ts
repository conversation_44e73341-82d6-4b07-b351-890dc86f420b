// <PERSON><PERSON>, MongoDB bağlantısı olmadığında kullanılacak sahte veritabanı işlevleri sağlar

export async function mockCreateRegistration(data: any) {
  console.log("Mock registration created:", data)
  return {
    ...data,
    _id: `mock-id-${Date.now()}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    status: "pending",
  }
}

export async function mockDbConnect() {
  console.log("Using mock database connection")
  return true
}
