import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { getToken } from 'next-auth/jwt'

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // API Docs koruması
  if (pathname.startsWith('/api/docs')) {
    // Geliştirme modunda serbest erişim
    if (process.env.NODE_ENV === 'development') {
      return NextResponse.next()
    }

    try {
      const token = await getToken({
        req: request,
        secret: process.env.NEXTAUTH_SECRET || "fallback-secret-do-not-use-in-production",
      })

      console.log('API Docs Middleware - Token:', token ? 'exists' : 'null')
      console.log('API Docs Middleware - Role:', token?.role)

      // Token yoksa veya admin değilse erişimi reddet
      if (!token || token.role !== 'admin') {
        console.log('API Docs Middleware - Access denied')

        // API endpoint için 403 döndür
        if (pathname.includes('swagger.json')) {
          return NextResponse.json(
            {
              error: 'Yet<PERSON>iz erişim',
              message: 'Bu API dokümantasyonuna erişim yetkiniz yok',
              code: 'UNAUTHORIZED_ACCESS'
            },
            { status: 403 }
          )
        }

        // Sayfa için unauthorized'a yönlendir
        const url = request.nextUrl.clone()
        url.pathname = '/unauthorized'
        url.searchParams.set('reason', 'api-docs')
        url.searchParams.set('attempted', pathname)
        return NextResponse.redirect(url)
      }

      console.log('API Docs Middleware - Access granted')
    } catch (error) {
      console.error('API Docs auth error:', error)
      const url = request.nextUrl.clone()
      url.pathname = '/unauthorized'
      url.searchParams.set('reason', 'api-docs')
      return NextResponse.redirect(url)
    }
  }

  // Admin rotalarını kontrol et
  if (pathname.startsWith('/admin')) {
    // Login sayfası ve API rotaları hariç tüm admin sayfaları için auth kontrolü
    if (pathname !== '/admin/login' && !pathname.startsWith('/admin/api')) {
      try {
        // JWT token'ı kontrol et
        const token = await getToken({
          req: request,
          secret: process.env.NEXTAUTH_SECRET || "fallback-secret-do-not-use-in-production",
        })

        console.log('Middleware - pathname:', pathname)
        console.log('Middleware - token:', token ? 'exists' : 'null')

        // Token yoksa login sayfasına yönlendir
        if (!token) {
          console.log('No token, redirecting to login')
          const loginUrl = new URL('/admin/login', request.url)
          loginUrl.searchParams.set('callbackUrl', pathname)
          return NextResponse.redirect(loginUrl)
        }

        // Token varsa ama admin değilse erişimi reddet
        if (token.role !== 'admin' && token.role !== 'editor') {
          console.log('Invalid role:', token.role)
          return NextResponse.redirect(new URL('/admin/login?error=unauthorized', request.url))
        }

        console.log('Auth successful, proceeding')
        // Her şey yolundaysa devam et
        return NextResponse.next()
      } catch (error) {
        console.error('Middleware auth error:', error)
        return NextResponse.redirect(new URL('/admin/login?error=auth_error', request.url))
      }
    }
  }

  // Admin olmayan rotalar için normal devam
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Korumalı rotaları yakala:
     * - /admin/... (admin sayfaları)
     * - /api/docs/... (API dokümantasyonu)
     * Ancak şunları hariç tut:
     * - /admin/login (login sayfası)
     * - Static dosyalar (_next/static, favicon.ico, vb.)
     */
    '/admin/dashboard/:path*',
    '/admin/blogs/:path*',
    '/admin/programs/:path*',
    '/admin/instructors/:path*',
    '/admin/gallery/:path*',
    '/admin/testimonials/:path*',
    '/admin/contacts/:path*',
    '/admin/site-settings/:path*',
    '/admin/profile/:path*',
    '/admin/registrations/:path*',
    '/api/docs/:path*',
  ],
}
