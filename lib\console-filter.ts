// Console error filtering for Swagger UI strict mode warnings

let originalConsoleError: typeof console.error | null = null

export function suppressSwaggerWarnings() {
  if (typeof window === 'undefined') return // Server-side'da çalışma
  
  if (!originalConsoleError) {
    originalConsoleError = console.error
  }

  console.error = (...args: any[]) => {
    // Swagger UI'nin deprecated lifecycle method uyarılarını filtrele
    const message = args[0]
    
    if (typeof message === 'string') {
      // UNSAFE_componentWillReceiveProps uyarılarını gizle
      if (message.includes('UNSAFE_componentWillReceiveProps') ||
          message.includes('componentWillReceiveProps') ||
          message.includes('ModelCollapse') ||
          message.includes('OperationContainer')) {
        return // Bu uyarıları gösterme
      }
      
      // React strict mode uyarılarını gizle (sadece Swagger UI için)
      if (message.includes('strict mode is not recommended') &&
          (message.includes('ModelCollapse') || message.includes('OperationContainer'))) {
        return
      }
    }
    
    // Diğer tüm hataları normal şekilde göster
    if (originalConsoleError) {
      originalConsoleError.apply(console, args)
    }
  }
}

export function restoreConsoleError() {
  if (originalConsoleError) {
    console.error = originalConsoleError
    originalConsoleError = null
  }
}

// Development modunda otomatik olarak aktifleştir
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  suppressSwaggerWarnings()
}
