import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Blog from "@/lib/models/blog"
import { handleApiError } from "@/lib/api-utils"

export async function PUT(
  request: Request, 
  { params }: { params: { id: string; commentId: string } }
) {
  try {
    await dbConnect()

    const { id, commentId } = await params
    const body = await request.json()
    const { isApproved } = body

    const blog = await Blog.findById(id)

    if (!blog) {
      return NextResponse.json({ success: false, error: "Blog bulunamadı" }, { status: 404 })
    }

    const comment = blog.comments.id(commentId)

    if (!comment) {
      return NextResponse.json({ success: false, error: "Yorum bulunamadı" }, { status: 404 })
    }

    comment.isApproved = isApproved
    await blog.save()

    return NextResponse.json({ success: true, message: "Yorum durumu güncellen<PERSON>" })
  } catch (error) {
    console.error("Error updating comment:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function DELETE(
  request: Request, 
  { params }: { params: { id: string; commentId: string } }
) {
  try {
    await dbConnect()

    const { id, commentId } = await params

    const blog = await Blog.findById(id)

    if (!blog) {
      return NextResponse.json({ success: false, error: "Blog bulunamadı" }, { status: 404 })
    }

    const comment = blog.comments.id(commentId)

    if (!comment) {
      return NextResponse.json({ success: false, error: "Yorum bulunamadı" }, { status: 404 })
    }

    blog.comments.pull(commentId)
    await blog.save()

    return NextResponse.json({ success: true, message: "Yorum silindi" })
  } catch (error) {
    console.error("Error deleting comment:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
