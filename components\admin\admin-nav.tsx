"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { signOut, useSession } from "next-auth/react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  LayoutDashboard,
  Users,
  BookOpen,
  ImageIcon,
  MessageSquare,
  UserCircle,
  LogOut,
  Menu,
  ChevronDown,
  Mail,
  FileText,
  Settings,
  ExternalLink,
} from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { useState, useEffect } from "react"

const navItems = [
  {
    title: "Dashboard",
    href: "/admin/dashboard",
    icon: <LayoutDashboard className="h-5 w-5" />,
  },
  {
    title: "Kayıtlar",
    href: "/admin/registrations",
    icon: <Users className="h-5 w-5" />,
  },
  {
    title: "Programlar",
    href: "/admin/programs",
    icon: <BookOpen className="h-5 w-5" />,
  },
  {
    title: "Eğitmenler",
    href: "/admin/instructors",
    icon: <UserCircle className="h-5 w-5" />,
  },
  {
    title: "Galeri",
    href: "/admin/gallery",
    icon: <ImageIcon className="h-5 w-5" />,
  },
  {
    title: "Blog",
    href: "/admin/blogs",
    icon: <FileText className="h-5 w-5" />,
  },
  {
    title: "Yorumlar",
    href: "/admin/testimonials",
    icon: <MessageSquare className="h-5 w-5" />,
  },
  {
    title: "İletişim",
    href: "/admin/contacts",
    icon: <Mail className="h-5 w-5" />,
  },
  {
    title: "Site Ayarları",
    href: "/admin/site-settings",
    icon: <Settings className="h-5 w-5" />,
  },
  {
    title: "API Docs",
    href: "/api/docs",
    icon: <FileText className="h-5 w-5" />,
    external: true,
  },
]

export function AdminNav({ user }) {
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)
  const { data: session } = useSession()
  const [updatedUser, setUpdatedUser] = useState(null)

  // localStorage'dan dev user data'yı yükle
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      const stored = localStorage.getItem('devUserData')
      if (stored) {
        try {
          const devData = JSON.parse(stored)
          setUpdatedUser(devData)
          console.log("AdminNav: Dev user data loaded from localStorage:", devData)
        } catch (error) {
          console.error("AdminNav: Error loading dev user data:", error)
        }
      }
    }
  }, [])

  // Session'dan güncel user bilgisini al, fallback olarak prop'tan gelen user'ı kullan
  const currentUser = updatedUser || session?.user || user

  // Profil güncelleme event'ini dinle
  useEffect(() => {
    const handleProfileUpdate = (event) => {
      console.log("Navigation: Profil güncelleme event'i alındı", event.detail)
      setUpdatedUser({
        ...currentUser,
        name: event.detail.name,
        email: event.detail.email,
        image: event.detail.image
      })
    }

    window.addEventListener('profileUpdated', handleProfileUpdate)

    return () => {
      window.removeEventListener('profileUpdated', handleProfileUpdate)
    }
  }, [currentUser])

  const handleLogout = async () => {
    // Geliştirme modunda sadece ana sayfaya yönlendir
    if (process.env.NODE_ENV === "development") {
      window.location.href = "/"
      return
    }

    // Production modunda NextAuth signOut kullan
    await signOut({ callbackUrl: "/" })
  }

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-white">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center">
          <Link href="/admin/dashboard" className="flex items-center gap-2">
            <span className="text-xl font-bold text-sky-600">Uçuş Atölyesi Admin</span>
          </Link>
        </div>
        <nav className="hidden md:flex items-center gap-6">
          {navItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              target={item.external ? "_blank" : undefined}
              rel={item.external ? "noopener noreferrer" : undefined}
              className={cn(
                "flex items-center gap-1 text-sm font-medium transition-colors hover:text-sky-600",
                pathname === item.href ? "text-sky-600" : "text-gray-600",
              )}
            >
              {item.icon}
              {item.title}
              {item.external && <ExternalLink className="h-3 w-3 ml-1" />}
            </Link>
          ))}
        </nav>
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center gap-2 h-auto p-2">
                <Avatar className="h-8 w-8">
                  {currentUser?.image ? (
                    <AvatarImage src={currentUser.image} alt={currentUser?.name || "Admin"} />
                  ) : null}
                  <AvatarFallback className="text-sm">
                    {currentUser?.name ? currentUser.name.split(' ').map(n => n.charAt(0)).join('').toUpperCase().slice(0, 2) : "AD"}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col items-start">
                  <span className="text-sm font-medium">{currentUser?.name || "Admin"}</span>
                  <span className="text-xs text-gray-500">{currentUser?.role || "admin"}</span>
                </div>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">{currentUser?.name || "Admin"}</p>
                  <p className="text-xs leading-none text-muted-foreground">{currentUser?.email || "<EMAIL>"}</p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/admin/profile" className="flex items-center">
                  <UserCircle className="h-4 w-4 mr-2" />
                  Profil Ayarları
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout} className="text-red-600 focus:text-red-600">
                <LogOut className="h-4 w-4 mr-2" />
                Çıkış Yap
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm" className="md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Menü</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <div className="flex flex-col space-y-4 mt-8">
                {navItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    target={item.external ? "_blank" : undefined}
                    rel={item.external ? "noopener noreferrer" : undefined}
                    className={cn(
                      "flex items-center gap-2 text-base font-medium transition-colors hover:text-sky-600",
                      pathname === item.href ? "text-sky-600" : "text-gray-600",
                    )}
                    onClick={() => setIsOpen(false)}
                  >
                    {item.icon}
                    {item.title}
                    {item.external && <ExternalLink className="h-4 w-4 ml-1" />}
                  </Link>
                ))}
                <Button
                  variant="ghost"
                  className="flex items-center justify-start gap-2 text-base font-medium text-red-500 hover:text-red-600 hover:bg-red-50 px-0"
                  onClick={handleLogout}
                >
                  <LogOut className="h-5 w-5" />
                  Çıkış Yap
                </Button>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}
