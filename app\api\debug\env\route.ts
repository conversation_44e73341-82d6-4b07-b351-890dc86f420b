import { NextResponse } from "next/server"

export async function GET() {
  return NextResponse.json({
    environment: {
      NODE_ENV: process.env.NODE_ENV,
      NEXTAUTH_URL: process.env.NEXTAUTH_URL,
      NEXTAUTH_SECRET_EXISTS: !!process.env.NEXTAUTH_SECRET,
      NEXTAUTH_SECRET_LENGTH: process.env.NEXTAUTH_SECRET?.length || 0,
      MONGODB_URI_EXISTS: !!process.env.MONGODB_URI,
      MONGODB_URI_PREVIEW: process.env.MONGODB_URI?.substring(0, 30) + "...",
      PORT: process.env.PORT,
      HOSTNAME: process.env.HOSTNAME,
      PWD: process.env.PWD
    },
    timestamp: new Date().toISOString()
  })
}
