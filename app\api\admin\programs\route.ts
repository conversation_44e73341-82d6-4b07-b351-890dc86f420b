import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Program from "@/lib/models/program"
import { handleApiError } from "@/lib/api-utils"
import { checkAdminAuth } from "@/lib/auth-utils"

export async function GET(request: Request) {
  try {
    // Admin auth kontrolü
    const authCheck = await checkAdminAuth()
    if (!authCheck.authorized) {
      return NextResponse.json({ success: false, error: authCheck.error }, { status: 401 })
    }

    await dbConnect()

    const programs = await Program.find({}).sort({ createdAt: -1 })

    return NextResponse.json({ success: true, data: programs })
  } catch (error) {
    console.error("Error fetching programs:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function POST(request: Request) {
  try {
    // Admin auth kontrolü
    const authCheck = await checkAdminAuth()
    if (!authCheck.authorized) {
      return NextResponse.json({ success: false, error: authCheck.error }, { status: 401 })
    }

    const body = await request.json()

    await dbConnect()

    const program = await Program.create(body)

    return NextResponse.json({ success: true, data: program }, { status: 201 })
  } catch (error) {
    console.error("Error creating program:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
