import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Blog from "@/lib/models/blog"

export async function POST(request: Request, { params }: { params: { slug: string } }) {
  try {
    await dbConnect()

    const { slug } = await params
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown'

    const blog = await Blog.findOne({ 
      slug: slug, 
      isPublished: true, 
      isActive: true 
    })

    if (!blog) {
      return NextResponse.json({ success: false, error: "Blog bulunamadı" }, { status: 404 })
    }

    // Daha önce beğenmiş mi kontrol et
    const hasLiked = blog.likedBy.includes(clientIP)

    if (hasLiked) {
      // Beğeniyi kaldır
      blog.likedBy = blog.likedBy.filter(ip => ip !== clientIP)
      blog.likes = Math.max(0, blog.likes - 1)
    } else {
      // Be<PERSON><PERSON> ekle
      blog.likedBy.push(clientIP)
      blog.likes += 1
    }

    await blog.save()

    return NextResponse.json({ 
      success: true, 
      data: { 
        likes: blog.likes, 
        hasLiked: !hasLiked 
      } 
    })
  } catch (error) {
    console.error("Error toggling blog like:", error)
    return NextResponse.json({ success: false, error: "Beğeni işlemi sırasında hata oluştu" }, { status: 500 })
  }
}
