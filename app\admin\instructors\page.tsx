"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/components/ui/use-toast"
import { PlusCircle, Pencil, Trash, Eye } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

export default function InstructorsPage() {
  const { toast } = useToast()
  const [instructors, setInstructors] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [instructorToDelete, setInstructorToDelete] = useState(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  const fetchInstructors = async () => {
    try {
      setIsLoading(true)
      const response = await fetch("/api/admin/instructors")
      const data = await response.json()

      if (data.success) {
        setInstructors(data.data)
      } else {
        console.error("Error fetching instructors:", data.error)
        toast({
          title: "Hata",
          description: "Eğitmenler yüklenirken bir hata oluştu.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error fetching instructors:", error)
      toast({
        title: "Hata",
        description: "Eğitmenler yüklenirken bir hata oluştu.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchInstructors()
  }, [])

  const handleDeleteClick = (instructor) => {
    setInstructorToDelete(instructor)
    setIsDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    try {
      const response = await fetch(`/api/admin/instructors/${instructorToDelete._id || instructorToDelete.id}`, {
        method: "DELETE",
      })

      const data = await response.json()

      if (data.success) {
        setInstructors((prev) => prev.filter((i) => (i._id || i.id) !== (instructorToDelete._id || instructorToDelete.id)))
        toast({
          title: "Eğitmen Silindi",
          description: `"${instructorToDelete.name}" eğitmeni başarıyla silindi.`,
        })
        setIsDeleteDialogOpen(false)
        setInstructorToDelete(null)
      } else {
        toast({
          title: "Hata",
          description: data.error || "Eğitmen silinirken bir hata oluştu.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error deleting instructor:", error)
      toast({
        title: "Hata",
        description: "Eğitmen silinirken bir hata oluştu.",
        variant: "destructive",
      })
    }
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("tr-TR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }).format(date)
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Eğitmenler</h1>
        <Button className="bg-sky-500 hover:bg-sky-600" asChild>
          <Link href="/admin/instructors/new">
            <PlusCircle className="h-4 w-4 mr-2" />
            Yeni Eğitmen
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Eğitmen Listesi</CardTitle>
          <CardDescription>Tüm eğitmenleri görüntüleyin ve yönetin.</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <p>Yükleniyor...</p>
            </div>
          ) : instructors.length === 0 ? (
            <div className="text-center py-8">
              <p>Henüz eğitmen bulunmuyor.</p>
              <Button className="mt-4 bg-sky-500 hover:bg-sky-600" asChild>
                <Link href="/admin/instructors/new">
                  <PlusCircle className="h-4 w-4 mr-2" />
                  İlk Eğitmeni Ekle
                </Link>
              </Button>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Fotoğraf</TableHead>
                    <TableHead>Ad Soyad</TableHead>
                    <TableHead>E-posta</TableHead>
                    <TableHead>Uzmanlık</TableHead>
                    <TableHead>Deneyim</TableHead>
                    <TableHead>Durum</TableHead>
                    <TableHead className="text-right">İşlemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {instructors.map((instructor) => (
                    <TableRow key={instructor._id || instructor.id}>
                      <TableCell>
                        <div className="w-10 h-10 relative rounded-full overflow-hidden">
                          <Image
                            src={instructor.image || "/placeholder-user.jpg"}
                            alt={instructor.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{instructor.name}</TableCell>
                      <TableCell>{instructor.email}</TableCell>
                      <TableCell>{instructor.expertise}</TableCell>
                      <TableCell>{instructor.experience}</TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 text-xs rounded-full ${
                            instructor.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {instructor.isActive ? "Aktif" : "Pasif"}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/instructors`} target="_blank">
                              <Eye className="h-4 w-4" />
                              <span className="sr-only">Görüntüle</span>
                            </Link>
                          </Button>
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/admin/instructors/${instructor._id || instructor.id}/edit`}>
                              <Pencil className="h-4 w-4" />
                              <span className="sr-only">Düzenle</span>
                            </Link>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => handleDeleteClick(instructor)}
                          >
                            <Trash className="h-4 w-4" />
                            <span className="sr-only">Sil</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <p className="text-sm text-gray-500">Toplam {instructors.length} eğitmen</p>
        </CardFooter>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Eğitmeni Silmek İstediğinize Emin misiniz?</AlertDialogTitle>
            <AlertDialogDescription>
              Bu işlem geri alınamaz. Bu eğitmen kalıcı olarak silinecek ve tüm ilişkili veriler kaldırılacaktır.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction className="bg-red-500 hover:bg-red-600" onClick={handleDeleteConfirm}>
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
