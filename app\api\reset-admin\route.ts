import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import User from "@/lib/models/user"
import bcrypt from "bcryptjs"

export async function POST() {
  try {
    console.log("=== ADMIN PASSWORD RESET ===")
    
    // Database bağlantısı
    await dbConnect()
    console.log("Database connected")
    
    // Admin kullanıcısını bul
    const adminUser = await User.findOne({ email: "<EMAIL>" })
    
    if (!adminUser) {
      return NextResponse.json({
        success: false,
        error: "Admin user not found"
      })
    }
    
    console.log("Admin user found:", adminUser.email)
    
    // Yeni şifreyi hash'le
    const salt = await bcrypt.genSalt(10)
    const hashedPassword = await bcrypt.hash("admin123", salt)
    
    console.log("New password hashed")
    
    // <PERSON><PERSON><PERSON><PERSON> güncelle
    adminUser.password = hashedPassword
    await adminUser.save()
    
    console.log("Password updated successfully")
    
    // Test et
    const testResult = await adminUser.comparePassword("admin123")
    console.log("Password test after update:", testResult)
    
    return NextResponse.json({
      success: true,
      message: "Admin password reset successfully",
      debug: {
        userEmail: adminUser.email,
        passwordUpdated: true,
        passwordTest: testResult,
        newPasswordLength: hashedPassword.length
      }
    })
    
  } catch (error) {
    console.error("Reset admin password error:", error)
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
