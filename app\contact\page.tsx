"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "sonner"
import { MapPin, Phone, Mail, Clock } from "lucide-react"

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (data.success) {
        toast.success("Mesajınız başarıyla gönderildi! En kısa sürede size geri dönüş yapacağız.")
        setFormData({
          name: "",
          email: "",
          phone: "",
          subject: "",
          message: "",
        })
      } else {
        toast.error(data.error || "Mesaj gönderilirken hata oluştu")
      }
    } catch (error) {
      console.error("Error submitting contact form:", error)
      toast.error("Mesaj gönderilirken hata oluştu")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div>
      {/* Hero Section */}
      <section className="relative py-20 bg-sky-600 text-white">
        <div className="container">
          <div className="max-w-3xl">
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">İletişim</h1>
            <p className="mt-6 text-xl text-white/90">Sorularınız için bize ulaşın veya merkezimizi ziyaret edin.</p>
          </div>
        </div>
      </section>

      {/* Contact Form and Info */}
      <section className="py-20 bg-white">
        <div className="container">
          <div className="grid md:grid-cols-2 gap-12">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 mb-6">Bize Yazın</h2>
              <form className="space-y-6" onSubmit={handleSubmit}>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="name" className="text-sm font-medium">
                      Adınız *
                    </label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      placeholder="Adınız Soyadınız"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="email" className="text-sm font-medium">
                      E-posta *
                    </label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label htmlFor="phone" className="text-sm font-medium">
                    Telefon
                  </label>
                  <Input
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder="05551234567"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="subject" className="text-sm font-medium">
                    Konu *
                  </label>
                  <Input
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    placeholder="Mesajınızın konusu"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="message" className="text-sm font-medium">
                    Mesajınız *
                  </label>
                  <Textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    placeholder="Mesajınızı buraya yazın..."
                    className="min-h-[120px]"
                    required
                  />
                </div>
                <Button type="submit" className="w-full bg-sky-500 hover:bg-sky-600" disabled={isSubmitting}>
                  {isSubmitting ? "Gönderiliyor..." : "Gönder"}
                </Button>
              </form>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="space-y-8"
            >
              <div>
                <h2 className="text-3xl font-bold tracking-tight text-gray-900 mb-6">İletişim Bilgileri</h2>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <MapPin className="h-5 w-5 text-sky-600 mt-1" />
                    <div>
                      <h3 className="font-semibold">Adres</h3>
                      <p className="text-gray-600">Şişli, İstanbul, Türkiye</p>
                      <p className="text-gray-600">Mecidiyeköy Mah. Büyükdere Cad. No: 123</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Phone className="h-5 w-5 text-sky-600 mt-1" />
                    <div>
                      <h3 className="font-semibold">Telefon</h3>
                      <p className="text-gray-600">+90 212 123 45 67</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Mail className="h-5 w-5 text-sky-600 mt-1" />
                    <div>
                      <h3 className="font-semibold">E-posta</h3>
                      <p className="text-gray-600"><EMAIL></p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Clock className="h-5 w-5 text-sky-600 mt-1" />
                    <div>
                      <h3 className="font-semibold">Çalışma Saatleri</h3>
                      <p className="text-gray-600">Pazartesi - Cuma: 09:00 - 18:00</p>
                      <p className="text-gray-600">Cumartesi: 10:00 - 16:00</p>
                      <p className="text-gray-600">Pazar: Kapalı</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="aspect-video relative rounded-lg overflow-hidden">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3008.2186900268313!2d28.979697075911947!3d41.04093491929961!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14cab7a2a2c3b963%3A0x7671d1b9817b8519!2zxLBzdGFuYnVs!5e0!3m2!1str!2str!4v1716057014983!5m2!1str!2str"
                  width="600"
                  height="450"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  className="absolute inset-0 w-full h-full"
                ></iframe>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-sky-50">
        <div className="container">
          <div className="text-center max-w-3xl mx-auto mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Sıkça Sorulan Sorular</h2>
            <p className="mt-4 text-lg text-gray-600">İletişim ve kayıt süreçleri hakkında en çok sorulan sorular.</p>
          </div>
          <div className="max-w-3xl mx-auto">
            {[
              {
                question: "Eğitimlere nasıl kayıt olabilirim?",
                answer:
                  "Eğitimlerimize kayıt olmak için web sitemizdeki 'Kayıt Ol' sayfasını kullanabilir veya doğrudan merkezimizi ziyaret edebilirsiniz.",
              },
              {
                question: "Eğitim ücretleri nasıl ödenir?",
                answer:
                  "Eğitim ücretleri nakit, kredi kartı veya banka havalesi ile ödenebilir. Ayrıca taksit imkanlarımız da bulunmaktadır.",
              },
              {
                question: "Eğitim grupları kaç kişiden oluşur?",
                answer:
                  "Eğitim gruplarımız maksimum 10 öğrenciden oluşmaktadır. Bu sayede her öğrenciye yeterli ilgi gösterilmesini sağlıyoruz.",
              },
              {
                question: "Eğitim iptali veya ertelemesi durumunda ne yapmalıyım?",
                answer:
                  "Eğitim başlangıç tarihinden en az 3 gün önce bildirilmesi durumunda, eğitim ücreti iade edilir veya başka bir tarihe ertelenir.",
              },
            ].map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="mb-6"
              >
                <h3 className="text-xl font-semibold mb-2">{faq.question}</h3>
                <p className="text-gray-600">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
