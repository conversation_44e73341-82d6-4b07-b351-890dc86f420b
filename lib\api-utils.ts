// Bu dosya, API isteklerini işlemek için yardımcı fonksiyonlar içerir

export async function handleApiError(error: any) {
  console.error("API Error:", error)

  // MongoDB bağlantı hatası
  if (error.message?.includes("MONGODB_URI")) {
    return {
      success: false,
      error: "Veritabanı bağlantısı kurulamadı. Lütfen .env.local dosyasını kontrol edin.",
      status: 500,
    }
  }

  // Genel hata
  return {
    success: false,
    error: "İşlem sırasında bir hata oluştu.",
    status: 500,
  }
}
