"use client"
import { motion } from "framer-motion"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

const faqs = [
  {
    question: "Hangi yaş grupları için eğitimleriniz var?",
    answer:
      "Eğitimlerimiz 6-14 yaş arası çocuklar için tasarlanmıştır. UA1 programımız 6-8 yaş, UA2 programımız 9-11 yaş ve UA3 programımız 12-14 yaş grubu çocuklar içindir.",
  },
  {
    question: "<PERSON>ğ<PERSON>mler ne kadar sürüyor?",
    answer:
      "Eğitim sürelerimiz programa göre değişmektedir. UA1 programı 8 hafta, UA2 programı 10 hafta ve UA3 programı 12 hafta sürmektedir. Her program haftada 2-3 saat ders içermektedir.",
  },
  {
    question: "Çocuğumun daha önce drone deneyimi olması gerekiyor mu?",
    answer:
      "<PERSON><PERSON><PERSON>, UA1 programımız hiçbir ön bilgi gerektirmez. Tüm konular sıfırdan anlatılmaktadır. UA2 ve UA3 programları için ise bir önceki seviyeyi tamamlamış olmak veya eşdeğer bilgiye sahip olmak gerekir.",
  },
  {
    question: "Eğitimler için ekipman sağlıyor musunuz?",
    answer:
      "Evet, tüm eğitimlerimizde gerekli drone'lar ve ekipmanlar tarafımızdan sağlanmaktadır. Öğrencilerin herhangi bir ekipman getirmesine gerek yoktur.",
  },
  {
    question: "Eğitim sonunda sertifika veriliyor mu?",
    answer:
      "Evet, tüm programlarımızı başarıyla tamamlayan öğrencilere sertifika verilmektedir. Bu sertifikalar, çocuğunuzun kazandığı becerileri belgelemektedir.",
  },
]

export function FAQSection() {
  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-900">
      <div className="container">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center max-w-3xl mx-auto mb-12"
        >
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
            Sıkça Sorulan Sorular
          </h2>
          <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
            Eğitim programlarımız hakkında en çok sorulan sorular ve cevapları
          </p>
        </motion.div>

        <div className="max-w-3xl mx-auto">
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <AccordionItem value={`item-${index}`}>
                  <AccordionTrigger className="text-left text-gray-900 dark:text-white">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-gray-600 dark:text-gray-300">{faq.answer}</AccordionContent>
                </AccordionItem>
              </motion.div>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  )
}
