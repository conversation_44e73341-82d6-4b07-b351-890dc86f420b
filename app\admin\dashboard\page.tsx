"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Users, BookOpen, UserCircle, ImageIcon, TrendingUp, Mail, MessageSquare } from "lucide-react"

export default function DashboardPage() {
  const [stats, setStats] = useState({
    registrations: { total: 0, pending: 0, approved: 0 },
    programs: { total: 0, active: 0 },
    instructors: { total: 0 },
    gallery: { total: 0 },
    testimonials: { total: 0, pending: 0, approved: 0 },
    contacts: { total: 0, new: 0 },
  })
  const [recentRegistrations, setRecentRegistrations] = useState([])
  const [recentTestimonials, setRecentTestimonials] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Gerçek API çağrıları
        const [registrationsRes, programsRes, instructorsRes, galleryRes, testimonialsRes, contactsRes] = await Promise.all([
          fetch("/api/admin/registrations"),
          fetch("/api/admin/programs"),
          fetch("/api/admin/instructors"),
          fetch("/api/admin/gallery"),
          fetch("/api/admin/testimonials"),
          fetch("/api/admin/contacts"),
        ])

        const registrationsData = await registrationsRes.json()
        const programsData = await programsRes.json()
        const instructorsData = await instructorsRes.json()
        const galleryData = await galleryRes.json()
        const testimonialsData = await testimonialsRes.json()
        const contactsData = await contactsRes.json()

        if (registrationsData.success) {
          const registrations = registrationsData.data || []
          const programs = programsData.success ? programsData.data || [] : []
          const instructors = instructorsData.success ? instructorsData.data || [] : []
          const gallery = galleryData.success ? galleryData.data || [] : []
          const testimonials = testimonialsData.success ? testimonialsData.data || [] : []
          const contacts = contactsData.success ? contactsData.data || [] : []

          setStats({
            registrations: {
              total: registrations.length,
              pending: registrations.filter(r => r.status === "pending").length,
              approved: registrations.filter(r => r.status === "approved").length,
            },
            programs: {
              total: programs.length,
              active: programs.filter(p => p.isActive).length,
            },
            instructors: {
              total: instructors.length,
            },
            gallery: {
              total: gallery.length,
            },
            testimonials: {
              total: testimonials.length,
              pending: testimonials.filter(t => t.status === "pending").length,
              approved: testimonials.filter(t => t.status === "approved").length,
            },
            contacts: {
              total: contacts.length,
              new: contacts.filter(c => c.status === "new").length,
            },
          })

          // Son kayıtları ayarla (son 5)
          setRecentRegistrations(
            registrations
              .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
              .slice(0, 5)
          )

          // Son yorumları ayarla (son 5)
          setRecentTestimonials(
            testimonials
              .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
              .slice(0, 5)
          )
        }
        setIsLoading(false)
      } catch (error) {
        console.error("Error fetching dashboard data:", error)
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  return (
    <div>
      <h1 className="text-3xl font-bold mb-8">Dashboard</h1>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Kayıt</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "..." : stats.registrations.total}</div>
            <p className="text-xs text-muted-foreground">
              {isLoading ? "..." : `${stats.registrations.pending} bekleyen, ${stats.registrations.approved} onaylı`}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Yorumlar</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "..." : stats.testimonials.total}</div>
            <p className="text-xs text-muted-foreground">
              {isLoading ? "..." : `${stats.testimonials.pending} bekleyen, ${stats.testimonials.approved} onaylı`}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">İletişim</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "..." : stats.contacts.total}</div>
            <p className="text-xs text-muted-foreground">
              {isLoading ? "..." : `${stats.contacts.new} yeni mesaj`}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktif Programlar</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "..." : stats.programs.active}</div>
            <p className="text-xs text-muted-foreground">
              {isLoading ? "..." : `Toplam ${stats.programs.total} program`}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Eğitmenler</CardTitle>
            <UserCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? "..." : stats.instructors.total}</div>
            <p className="text-xs text-muted-foreground">Aktif eğitmen sayısı</p>
          </CardContent>
        </Card>
      </div>

      <div className="mt-8">
        <Tabs defaultValue="registrations">
          <TabsList>
            <TabsTrigger value="registrations">Son Kayıtlar</TabsTrigger>
            <TabsTrigger value="testimonials">Son Yorumlar</TabsTrigger>
          </TabsList>
          <TabsContent value="registrations" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>Son Kayıtlar</CardTitle>
                <CardDescription>Son 5 kayıt başvurusu</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <p>Yükleniyor...</p>
                ) : recentRegistrations.length === 0 ? (
                  <p className="text-center text-gray-500 py-4">Henüz kayıt bulunmuyor</p>
                ) : (
                  <div className="space-y-4">
                    {recentRegistrations.map((registration) => (
                      <div key={registration._id} className="flex items-center justify-between border-b pb-2">
                        <div>
                          <p className="font-medium">{registration.childName}</p>
                          <p className="text-sm text-gray-500">
                            {registration.parentName} • {registration.program?.toUpperCase() || "Program belirtilmemiş"}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-500">
                            {new Date(registration.createdAt).toLocaleDateString("tr-TR")}
                          </span>
                          <span
                            className={`px-2 py-1 text-xs rounded-full ${
                              registration.status === "pending"
                                ? "bg-yellow-100 text-yellow-800"
                                : registration.status === "approved"
                                ? "bg-green-100 text-green-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {registration.status === "pending"
                              ? "Bekliyor"
                              : registration.status === "approved"
                              ? "Onaylandı"
                              : "Reddedildi"}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="testimonials" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>Son Yorumlar</CardTitle>
                <CardDescription>Son 5 veli yorumu</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <p>Yükleniyor...</p>
                ) : recentTestimonials.length === 0 ? (
                  <p className="text-center text-gray-500 py-4">Henüz yorum bulunmuyor</p>
                ) : (
                  <div className="space-y-4">
                    {recentTestimonials.map((testimonial) => (
                      <div key={testimonial._id} className="flex items-center justify-between border-b pb-2">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <p className="font-medium">{testimonial.name}</p>
                            <span
                              className={`px-2 py-1 text-xs rounded-full ${
                                testimonial.status === "pending"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : testimonial.status === "approved"
                                  ? "bg-green-100 text-green-800"
                                  : "bg-red-100 text-red-800"
                              }`}
                            >
                              {testimonial.status === "pending"
                                ? "Bekliyor"
                                : testimonial.status === "approved"
                                ? "Onaylandı"
                                : "Reddedildi"}
                            </span>
                          </div>
                          <p className="text-sm text-gray-500">{testimonial.role || "Rol belirtilmemiş"}</p>
                          <p className="text-sm italic mt-1">"{testimonial.quote.substring(0, 60)}..."</p>
                        </div>
                        <span className="text-sm text-gray-500">
                          {new Date(testimonial.createdAt).toLocaleDateString("tr-TR")}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Sistem Özeti</CardTitle>
            <CardDescription>Genel sistem durumu</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-sky-500 mb-2">
                  {isLoading ? "..." : stats.registrations.total}
                </div>
                <p className="text-sm text-gray-600">Toplam Kayıt</p>
                <div className="mt-2 text-xs text-gray-500">
                  {isLoading ? "..." : `${stats.registrations.pending} bekleyen`}
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-500 mb-2">
                  {isLoading ? "..." : stats.testimonials.approved}
                </div>
                <p className="text-sm text-gray-600">Onaylı Yorum</p>
                <div className="mt-2 text-xs text-gray-500">
                  {isLoading ? "..." : `${stats.testimonials.pending} bekleyen`}
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-500 mb-2">
                  {isLoading ? "..." : stats.contacts.total}
                </div>
                <p className="text-sm text-gray-600">İletişim Mesajı</p>
                <div className="mt-2 text-xs text-gray-500">
                  {isLoading ? "..." : `${stats.contacts.new} yeni`}
                </div>
              </div>
            </div>
            <div className="mt-6 pt-6 border-t">
              <div className="flex items-center justify-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-sky-500" />
                  <span>Sistem aktif ve çalışıyor</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>MongoDB bağlantısı başarılı</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
