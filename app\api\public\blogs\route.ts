import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Blog from "@/lib/models/blog"

export async function GET(request: Request) {
  try {
    await dbConnect()

    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const limit = parseInt(searchParams.get('limit') || '10')
    const page = parseInt(searchParams.get('page') || '1')
    const skip = (page - 1) * limit

    let query = { isPublished: true, isActive: true }
    
    if (category && category !== 'all') {
      query = { ...query, category }
    }

    const blogs = await Blog.find(query)
      .select('title slug excerpt author category tags readTime likes publishedAt featuredImage')
      .sort({ publishedAt: -1 })
      .skip(skip)
      .limit(limit)

    const total = await Blog.countDocuments(query)

    return NextResponse.json({ 
      success: true, 
      data: blogs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error("Error fetching public blogs:", error)
    return NextResponse.json({ success: false, error: "Bloglar yüklenirken hata oluştu" }, { status: 500 })
  }
}
