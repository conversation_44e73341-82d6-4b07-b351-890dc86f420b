import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Gallery from "@/lib/models/gallery"
import { handleApiError } from "@/lib/api-utils"
import { extractYouTubeVideoId, getYouTubeVideoInfo, isValidYouTubeUrl } from "@/lib/youtube-utils"

export async function GET(request: Request) {
  try {
    await dbConnect()

    const galleryItems = await Gallery.find({}).sort({ createdAt: -1 })

    return NextResponse.json({ success: true, data: galleryItems })
  } catch (error) {
    console.error("Error fetching gallery items:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    await dbConnect()

    // Video ise YouTube bilgilerini çek
    if (body.type === "video" && body.videoUrl) {
      // YouTube URL'sini doğrula
      if (!isValidYouTubeUrl(body.videoUrl)) {
        return NextResponse.json(
          { success: false, error: "Geçersiz YouTube URL'si" },
          { status: 400 }
        )
      }

      // Video ID'sini çıkar
      const videoId = extractYouTubeVideoId(body.videoUrl)
      if (!videoId) {
        return NextResponse.json(
          { success: false, error: "YouTube video ID'si çıkarılamadı" },
          { status: 400 }
        )
      }

      try {
        // YouTube'dan video bilgilerini çek
        const videoInfo = await getYouTubeVideoInfo(videoId)

        // Body'yi video bilgileriyle güncelle
        body.videoId = videoInfo.videoId
        body.videoTitle = videoInfo.title
        body.videoDescription = videoInfo.description
        body.videoThumbnail = videoInfo.thumbnail
        body.videoDuration = videoInfo.duration

        // Eğer title boşsa video title'ını kullan
        if (!body.title) {
          body.title = videoInfo.title
        }
      } catch (error) {
        console.error("YouTube video info error:", error)
        return NextResponse.json(
          { success: false, error: "YouTube video bilgileri alınamadı. Video URL'sini kontrol edin." },
          { status: 400 }
        )
      }
    }

    const galleryItem = await Gallery.create(body)

    return NextResponse.json({ success: true, data: galleryItem }, { status: 201 })
  } catch (error) {
    console.error("Error creating gallery item:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
