"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import { PlusCircle, Pencil, Trash, Eye, Heart, MessageCircle } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

export default function BlogsPage() {
  const [blogs, setBlogs] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [blogToDelete, setBlogToDelete] = useState(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  const fetchBlogs = async () => {
    try {
      setIsLoading(true)
      const response = await fetch("/api/admin/blogs")
      const data = await response.json()

      if (data.success) {
        setBlogs(data.data)
      } else {
        console.error("Error fetching blogs:", data.error)
        toast.error("Bloglar yüklenirken bir hata oluştu.")
      }
    } catch (error) {
      console.error("Error fetching blogs:", error)
      toast.error("Bloglar yüklenirken bir hata oluştu.")
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchBlogs()
  }, [])

  const handleDeleteClick = (blog) => {
    setBlogToDelete(blog)
    setIsDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    try {
      const response = await fetch(`/api/admin/blogs/${blogToDelete._id}`, {
        method: "DELETE",
      })

      const data = await response.json()

      if (data.success) {
        setBlogs((prev) => prev.filter((blog) => blog._id !== blogToDelete._id))
        toast.success(`"${blogToDelete.title}" başarıyla silindi.`)
        setIsDeleteDialogOpen(false)
        setBlogToDelete(null)
      } else {
        toast.error(data.error || "Blog silinirken bir hata oluştu.")
      }
    } catch (error) {
      console.error("Error deleting blog:", error)
      toast.error("Blog silinirken bir hata oluştu.")
    }
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("tr-TR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }).format(date)
  }

  const getCategoryName = (category) => {
    const categories = {
      "eğitim": "Eğitim",
      "teknoloji": "Teknoloji",
      "etkinlik": "Etkinlik",
      "haberler": "Haberler",
      "ipuçları": "İpuçları"
    }
    return categories[category] || category
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Blog Yazıları</h1>
        <Button className="bg-sky-500 hover:bg-sky-600" asChild>
          <Link href="/admin/blogs/new">
            <PlusCircle className="h-4 w-4 mr-2" />
            Yeni Blog Yazısı
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Blog Listesi</CardTitle>
          <CardDescription>Tüm blog yazılarını görüntüleyin ve yönetin.</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <p>Yükleniyor...</p>
            </div>
          ) : blogs.length === 0 ? (
            <div className="text-center py-8">
              <p>Henüz blog yazısı bulunmuyor.</p>
              <Button className="mt-4 bg-sky-500 hover:bg-sky-600" asChild>
                <Link href="/admin/blogs/new">
                  <PlusCircle className="h-4 w-4 mr-2" />
                  İlk Blog Yazısını Ekle
                </Link>
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {blogs.map((blog) => (
                <Card key={blog._id} className="border-l-4 border-l-sky-500">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="text-lg font-semibold">{blog.title}</h3>
                          <Badge variant={blog.isPublished ? "default" : "secondary"}>
                            {blog.isPublished ? "Yayında" : "Taslak"}
                          </Badge>
                          <Badge variant="outline">
                            {getCategoryName(blog.category)}
                          </Badge>
                        </div>
                        <p className="text-gray-600 mb-3">{blog.excerpt}</p>
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span>Yazar: {blog.author}</span>
                          <span>Okuma: {blog.readTime} dk</span>
                          <span className="flex items-center gap-1">
                            <Heart className="h-4 w-4" />
                            {blog.likes}
                          </span>
                          <span className="flex items-center gap-1">
                            <MessageCircle className="h-4 w-4" />
                            {blog.comments?.length || 0}
                            {blog.comments?.filter(c => !c.isApproved).length > 0 && (
                              <span className="bg-red-100 text-red-800 px-1 py-0.5 rounded text-xs">
                                {blog.comments.filter(c => !c.isApproved).length} beklemede
                              </span>
                            )}
                          </span>
                          <span>{formatDate(blog.publishedAt || blog.createdAt)}</span>
                        </div>
                      </div>
                      <div className="flex gap-2 ml-4">
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/blog/${blog.slug}`} target="_blank">
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/admin/blogs/${blog._id}/edit`}>
                            <Pencil className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          onClick={() => handleDeleteClick(blog)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <p className="text-sm text-gray-500">Toplam {blogs.length} blog yazısı</p>
        </CardFooter>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Blog Yazısını Silmek İstediğinize Emin misiniz?</AlertDialogTitle>
            <AlertDialogDescription>
              Bu işlem geri alınamaz. Bu blog yazısı ve tüm yorumları kalıcı olarak silinecektir.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction className="bg-red-500 hover:bg-red-600" onClick={handleDeleteConfirm}>
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
