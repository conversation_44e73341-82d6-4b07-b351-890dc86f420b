"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { toast } from "sonner"
import { ArrowLeft, Save, Play, Image as ImageIcon, Loader2, Camera, Upload } from "lucide-react"

export default function NewGalleryPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingVideo, setIsLoadingVideo] = useState(false)
  const [selectedFile, setSelectedFile] = useState(null)
  const [previewUrl, setPreviewUrl] = useState("")
  const [isUploading, setIsUploading] = useState(false)
  const [formData, setFormData] = useState({
    type: searchParams.get("type") || "image",
    title: "",
    description: "",
    image: "",
    videoUrl: "",
    videoId: "",
    videoTitle: "",
    videoDescription: "",
    videoThumbnail: "",
    category: "",
    isActive: true,
  })

  const categories = [
    { value: "ua1", label: "UA1 - Giriş Seviyesi" },
    { value: "ua2", label: "UA2 - Orta Seviye" },
    { value: "ua3", label: "UA3 - İleri Seviye" },
    { value: "workshop", label: "Atölye Çalışması" },
    { value: "teamwork", label: "Takım Çalışması" },
    { value: "graduation", label: "Mezuniyet" },
    { value: "event", label: "Etkinlik" },
    { value: "other", label: "Diğer" },
  ]

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSwitchChange = (name, checked) => {
    setFormData((prev) => ({ ...prev, [name]: checked }))
  }

  const handleFileChange = (e) => {
    const file = e.target.files[0]
    if (file) {
      // Dosya boyutu kontrolü (5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("Dosya boyutu 5MB'dan küçük olmalıdır.")
        return
      }

      // Dosya tipi kontrolü
      if (!file.type.startsWith('image/')) {
        toast.error("Sadece resim dosyaları yüklenebilir.")
        return
      }

      setSelectedFile(file)

      // Preview oluştur
      const reader = new FileReader()
      reader.onload = (e) => {
        setPreviewUrl(e.target.result)
      }
      reader.readAsDataURL(file)
    }
  }

  const uploadImage = async (file) => {
    console.log("Gallery: Upload image fonksiyonu çağrıldı:", file.name)

    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', 'gallery')

    console.log("Gallery: FormData hazırlandı")

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    })

    console.log("Gallery: Upload response status:", response.status)
    const data = await response.json()
    console.log("Gallery: Upload response data:", data)

    if (!data.success) {
      throw new Error(data.error || 'Resim yüklenirken hata oluştu')
    }

    return data.url
  }

  const handleVideoUrlChange = async (e) => {
    const url = e.target.value
    setFormData((prev) => ({ ...prev, videoUrl: url }))

    // YouTube URL'si girildiğinde otomatik olarak video bilgilerini çek
    if (url && url.includes("youtube.com") || url.includes("youtu.be")) {
      setIsLoadingVideo(true)
      try {
        const response = await fetch("/api/admin/gallery/youtube-info", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ videoUrl: url }),
        })

        const data = await response.json()

        if (data.success) {
          setFormData((prev) => ({
            ...prev,
            videoId: data.data.videoId,
            videoTitle: data.data.title,
            videoDescription: data.data.description,
            videoThumbnail: data.data.thumbnail,
            title: prev.title || data.data.title, // Eğer title boşsa video title'ını kullan
          }))
          toast.success("Video bilgileri başarıyla yüklendi!")
        } else {
          toast.error(data.error || "Video bilgileri alınamadı")
        }
      } catch (error) {
        console.error("Error fetching video info:", error)
        toast.error("Video bilgileri alınırken hata oluştu")
      } finally {
        setIsLoadingVideo(false)
      }
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)

    console.log("Gallery: Form submit başladı")
    console.log("Gallery: Form data:", formData)
    console.log("Gallery: Selected file:", selectedFile)

    try {
      let imageUrl = formData.image

      // Eğer fotoğraf tipi seçildi ve dosya yüklendiyse
      if (formData.type === "image" && selectedFile) {
        console.log("Gallery: Resim yükleniyor...")
        setIsUploading(true)
        imageUrl = await uploadImage(selectedFile)
        console.log("Gallery: Yüklenen resim URL:", imageUrl)
        setIsUploading(false)
      }

      const submitData = {
        ...formData,
        image: imageUrl
      }

      console.log("Gallery: API'ye gönderilecek data:", submitData)

      const response = await fetch("/api/admin/gallery", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      })

      const data = await response.json()

      if (data.success) {
        toast.success(`Yeni ${formData.type === "video" ? "video" : "fotoğraf"} başarıyla oluşturuldu.`)
        router.push("/admin/gallery")
      } else {
        toast.error(data.error || "Galeri öğesi oluşturulurken bir hata oluştu.")
      }
    } catch (error) {
      console.error("Error creating gallery item:", error)
      toast.error("Galeri öğesi oluşturulurken bir hata oluştu.")
    } finally {
      setIsLoading(false)
      setIsUploading(false)
    }
  }

  return (
    <div>
      <div className="flex items-center gap-4 mb-8">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/gallery">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Geri
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">
          Yeni {formData.type === "video" ? "Video" : "Fotoğraf"} Ekle
        </h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{formData.type === "video" ? "Video" : "Fotoğraf"} Bilgileri</CardTitle>
          <CardDescription>
            Yeni bir {formData.type === "video" ? "video" : "fotoğraf"} ekleyin.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Tip Seçimi */}
            <div className="space-y-3">
              <Label>İçerik Tipi</Label>
              <RadioGroup
                value={formData.type}
                onValueChange={(value) => handleSelectChange("type", value)}
                className="flex gap-6"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="image" id="image" />
                  <Label htmlFor="image" className="flex items-center gap-2 cursor-pointer">
                    <ImageIcon className="h-4 w-4" />
                    Fotoğraf
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="video" id="video" />
                  <Label htmlFor="video" className="flex items-center gap-2 cursor-pointer">
                    <Play className="h-4 w-4" />
                    Video
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {/* Video URL (sadece video tipinde) */}
            {formData.type === "video" && (
              <div className="space-y-2">
                <Label htmlFor="videoUrl">YouTube Video URL</Label>
                <div className="relative">
                  <Input
                    id="videoUrl"
                    name="videoUrl"
                    value={formData.videoUrl}
                    onChange={handleVideoUrlChange}
                    placeholder="https://www.youtube.com/watch?v=..."
                    required
                  />
                  {isLoadingVideo && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                    </div>
                  )}
                </div>
                <p className="text-sm text-gray-500">
                  YouTube video URL'sini girin. Video bilgileri otomatik olarak yüklenecektir.
                </p>
              </div>
            )}

            {/* Video Önizleme (video tipinde ve video yüklendiyse) */}
            {formData.type === "video" && formData.videoThumbnail && (
              <div className="space-y-2">
                <Label>Video Önizleme</Label>
                <div className="relative w-full max-w-md">
                  <img
                    src={formData.videoThumbnail}
                    alt={formData.videoTitle}
                    className="w-full rounded-lg"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center rounded-lg">
                    <div className="bg-red-600 rounded-full p-2">
                      <Play className="h-4 w-4 text-white fill-white" />
                    </div>
                  </div>
                </div>
                {formData.videoTitle && (
                  <p className="text-sm font-medium">{formData.videoTitle}</p>
                )}
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="title">Başlık</Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                placeholder={formData.type === "video" ? "Video başlığı (otomatik doldurulur)" : "UA1 Sınıfı Drone Uçuşu"}
                required
              />
              {formData.type === "video" && (
                <p className="text-sm text-gray-500">
                  Video URL'si girildiğinde otomatik olarak doldurulur, değiştirebilirsiniz.
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Açıklama</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder={formData.type === "video" ? "Video açıklaması..." : "Fotoğraf açıklaması..."}
                rows={3}
                required
              />
            </div>

            {/* Fotoğraf Yükleme (sadece image tipinde) */}
            {formData.type === "image" && (
              <div className="space-y-4">
                <Label>Fotoğraf</Label>

                {/* Dosya Seçici */}
                <div className="flex flex-col items-center space-y-4 p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-sky-400 transition-colors">
                  {previewUrl ? (
                    <div className="relative">
                      <img
                        src={previewUrl}
                        alt="Preview"
                        className="w-full max-w-md h-48 object-cover rounded-lg"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="absolute top-2 right-2"
                        onClick={() => {
                          setSelectedFile(null)
                          setPreviewUrl("")
                        }}
                      >
                        Değiştir
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center">
                      <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-lg font-medium text-gray-700 mb-2">Fotoğraf Yükle</p>
                      <p className="text-sm text-gray-500 mb-4">
                        JPG, PNG veya GIF formatında, maksimum 5MB
                      </p>
                    </div>
                  )}

                  <Label htmlFor="imageFile" className="cursor-pointer">
                    <div className="flex items-center gap-2 px-4 py-2 bg-sky-50 hover:bg-sky-100 rounded-lg border border-sky-200 transition-colors">
                      <Camera className="h-4 w-4 text-sky-600" />
                      <span className="text-sm text-sky-600">
                        {previewUrl ? "Farklı Fotoğraf Seç" : "Fotoğraf Seç"}
                      </span>
                    </div>
                  </Label>
                  <Input
                    id="imageFile"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                </div>

                {isUploading && (
                  <div className="flex items-center justify-center gap-2 text-sky-600">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm">Fotoğraf yükleniyor...</span>
                  </div>
                )}
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="category">Kategori</Label>
              <Select value={formData.category} onValueChange={(value) => handleSelectChange("category", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Kategori seçin" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => handleSwitchChange("isActive", checked)}
              />
              <Label htmlFor="isActive">Aktif</Label>
            </div>

            <div className="flex gap-4">
              <Button type="submit" disabled={isLoading} className="bg-sky-500 hover:bg-sky-600">
                <Save className="h-4 w-4 mr-2" />
                {isLoading ? "Kaydediliyor..." : "Kaydet"}
              </Button>
              <Button type="button" variant="outline" asChild>
                <Link href="/admin/gallery">İptal</Link>
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
