import swaggerJSDoc from 'swagger-jsdoc'

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Uçuş Atölyesi API',
      version: '1.0.0',
      description: 'Uçuş Atölyesi web sitesi için REST API dokümantasyonu',
      contact: {
        name: '<PERSON><PERSON><PERSON><PERSON> Atölyesi',
        email: '<EMAIL>',
        url: 'https://ucusatolyesi.com'
      }
    },
    servers: [
      {
        url: process.env.NODE_ENV === 'production' 
          ? 'https://ucusatolyesi.com' 
          : 'http://localhost:3000',
        description: process.env.NODE_ENV === 'production' 
          ? 'Production server' 
          : 'Development server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false
            },
            error: {
              type: 'string',
              example: 'Hata mesajı'
            }
          }
        },
        Success: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true
            },
            message: {
              type: 'string',
              example: '<PERSON><PERSON><PERSON> başarılı'
            }
          }
        },
        Program: {
          type: 'object',
          properties: {
            _id: {
              type: 'string',
              example: '507f1f77bcf86cd799439011'
            },
            title: {
              type: 'string',
              example: 'UA1 - Giriş Seviyesi'
            },
            slug: {
              type: 'string',
              example: 'ua1'
            },
            description: {
              type: 'string',
              example: 'Drone uçuşuna giriş seviyesi eğitimi'
            },
            age: {
              type: 'string',
              example: '6-8 yaş'
            },
            price: {
              type: 'string',
              example: '3.500 TL'
            },
            duration: {
              type: 'string',
              example: '8 hafta'
            },
            features: {
              type: 'array',
              items: {
                type: 'string'
              },
              example: ['Temel uçuş prensipleri', 'Güvenlik kuralları']
            },
            isActive: {
              type: 'boolean',
              example: true
            },
            createdAt: {
              type: 'string',
              format: 'date-time'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time'
            }
          }
        },
        Blog: {
          type: 'object',
          properties: {
            _id: {
              type: 'string',
              example: '507f1f77bcf86cd799439011'
            },
            title: {
              type: 'string',
              example: 'Drone Teknolojisinin Geleceği'
            },
            slug: {
              type: 'string',
              example: 'drone-teknolojisinin-gelecegi'
            },
            excerpt: {
              type: 'string',
              example: 'Drone teknolojisinin gelişimi ve gelecekteki potansiyeli'
            },
            content: {
              type: 'string',
              example: 'Blog yazısının tam içeriği...'
            },
            author: {
              type: 'string',
              example: 'Uçuş Atölyesi'
            },
            category: {
              type: 'string',
              example: 'teknoloji'
            },
            tags: {
              type: 'array',
              items: {
                type: 'string'
              },
              example: ['drone', 'teknoloji', 'gelecek']
            },
            featuredImage: {
              type: 'string',
              example: '/blog-image.jpg'
            },
            isPublished: {
              type: 'boolean',
              example: true
            },
            readTime: {
              type: 'number',
              example: 5
            },
            likes: {
              type: 'number',
              example: 10
            },
            publishedAt: {
              type: 'string',
              format: 'date-time'
            },
            createdAt: {
              type: 'string',
              format: 'date-time'
            }
          }
        },
        Instructor: {
          type: 'object',
          properties: {
            _id: {
              type: 'string',
              example: '507f1f77bcf86cd799439011'
            },
            name: {
              type: 'string',
              example: 'Dr. Ahmet Kaya'
            },
            role: {
              type: 'string',
              example: 'Baş Eğitmen'
            },
            email: {
              type: 'string',
              example: '<EMAIL>'
            },
            phone: {
              type: 'string',
              example: '05551234567'
            },
            expertise: {
              type: 'string',
              example: 'Drone Teknolojisi, Havacılık Mühendisliği'
            },
            experience: {
              type: 'string',
              example: '8 yıl'
            },
            bio: {
              type: 'string',
              example: 'Eğitmen hakkında detaylı bilgi...'
            },
            image: {
              type: 'string',
              example: '/instructor-1.jpg'
            },
            isActive: {
              type: 'boolean',
              example: true
            },
            createdAt: {
              type: 'string',
              format: 'date-time'
            }
          }
        },
        Gallery: {
          type: 'object',
          properties: {
            _id: {
              type: 'string',
              example: '507f1f77bcf86cd799439011'
            },
            type: {
              type: 'string',
              enum: ['image', 'video'],
              example: 'image'
            },
            title: {
              type: 'string',
              example: 'UA1 Sınıfı Drone Uçuşu'
            },
            description: {
              type: 'string',
              example: 'Öğrencilerimizin ilk drone uçuş deneyimi'
            },
            image: {
              type: 'string',
              example: '/gallery-1.jpg'
            },
            videoUrl: {
              type: 'string',
              example: 'https://www.youtube.com/watch?v=...'
            },
            category: {
              type: 'string',
              example: 'ua1'
            },
            isActive: {
              type: 'boolean',
              example: true
            },
            createdAt: {
              type: 'string',
              format: 'date-time'
            }
          }
        },
        Contact: {
          type: 'object',
          properties: {
            _id: {
              type: 'string',
              example: '507f1f77bcf86cd799439011'
            },
            name: {
              type: 'string',
              example: 'Ahmet Yılmaz'
            },
            email: {
              type: 'string',
              example: '<EMAIL>'
            },
            phone: {
              type: 'string',
              example: '05551234567'
            },
            subject: {
              type: 'string',
              example: 'Eğitim Hakkında Bilgi'
            },
            message: {
              type: 'string',
              example: 'Merhaba, eğitimleriniz hakkında bilgi almak istiyorum.'
            },
            status: {
              type: 'string',
              enum: ['new', 'read', 'replied'],
              example: 'new'
            },
            createdAt: {
              type: 'string',
              format: 'date-time'
            }
          }
        }
      }
    },
    tags: [
      {
        name: 'Public',
        description: 'Herkese açık API endpoints'
      },
      {
        name: 'Admin',
        description: 'Admin paneli için API endpoints (Yetkilendirme gerekli)'
      },
      {
        name: 'Programs',
        description: 'Eğitim programları'
      },
      {
        name: 'Blogs',
        description: 'Blog yazıları'
      },
      {
        name: 'Instructors',
        description: 'Eğitmenler'
      },
      {
        name: 'Gallery',
        description: 'Galeri'
      },
      {
        name: 'Contacts',
        description: 'İletişim mesajları'
      },
      {
        name: 'Upload',
        description: 'Dosya yükleme'
      }
    ]
  },
  apis: [
    './app/api/**/*.ts',
    './app/api/**/*.js'
  ]
}

const swaggerSpec = swaggerJSDoc(options)

export default swaggerSpec
