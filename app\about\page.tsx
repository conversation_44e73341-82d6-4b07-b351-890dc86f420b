"use client"

import Image from "next/image"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { CheckCircle, Users, Lightbulb, BookOpen, Award, Rocket } from "lucide-react"

export default function AboutPage() {
  return (
    <div>
      {/* Hero Section */}
      <section className="relative py-20 bg-sky-600 text-white">
        <div className="container">
          <div className="max-w-3xl">
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">Hakkımızda</h1>
            <p className="mt-6 text-xl text-white/90">
              Uçuş Atölyesi'nin hikayes<PERSON>, değerleri ve eğitim felsefesi hakkında bilgi edinin.
            </p>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20 bg-white">
        <div className="container">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Misyon ve Vizyonumuz</h2>
              <div className="mt-6 space-y-6 text-lg text-gray-600">
                <p>
                  <strong>Misyonumuz:</strong> Çocuklara drone teknolojisi ve havacılık bilimini eğlenceli ve eğitici
                  bir ortamda öğreterek, onların teknoloji, bilim ve mühendislik alanlarında güçlü bir temel
                  oluşturmalarını sağlamak.
                </p>
                <p>
                  <strong>Vizyonumuz:</strong> Türkiye'nin önde gelen çocuk STEM eğitim merkezi olarak, geleceğin
                  teknoloji liderlerini yetiştirmek ve havacılık sektörüne ilham vermek.
                </p>
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative h-[400px] rounded-lg overflow-hidden"
            >
              <Image src="/about-mission.jpg" alt="Uçuş Atölyesi Misyon" fill className="object-cover" />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Why Us */}
      <section className="py-20 bg-sky-50">
        <div className="container">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Neden Biz?</h2>
            <p className="mt-4 text-lg text-gray-600">
              Uçuş Atölyesi'ni diğer eğitim merkezlerinden ayıran özelliklerimiz.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                title: "Uzman Eğitmenler",
                description:
                  "Alanında uzman, deneyimli ve çocuklarla çalışmayı seven eğitmenlerimizle kaliteli eğitim.",
                icon: <Users className="h-10 w-10 text-sky-600" />,
              },
              {
                title: "Modern Ekipmanlar",
                description: "En son teknoloji drone'lar ve eğitim materyalleri ile pratik yapma imkanı.",
                icon: <Rocket className="h-10 w-10 text-sky-600" />,
              },
              {
                title: "Küçük Gruplar",
                description: "Her çocuğa özel ilgi gösterebilmek için sınırlı sayıda öğrenci ile eğitim.",
                icon: <Users className="h-10 w-10 text-sky-600" />,
              },
              {
                title: "Güvenli Ortam",
                description: "Çocukların güvenliği için özel olarak tasarlanmış atölye alanları ve ekipmanlar.",
                icon: <CheckCircle className="h-10 w-10 text-sky-600" />,
              },
              {
                title: "Yaratıcı Müfredat",
                description: "Çocukların yaratıcılığını ve problem çözme becerilerini geliştiren özel müfredat.",
                icon: <Lightbulb className="h-10 w-10 text-sky-600" />,
              },
              {
                title: "Sertifikalı Eğitim",
                description: "Eğitim sonunda uluslararası geçerliliği olan sertifika alma imkanı.",
                icon: <Award className="h-10 w-10 text-sky-600" />,
              },
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full">
                  <CardContent className="pt-6">
                    <div className="mb-4">{feature.icon}</div>
                    <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                    <p className="text-gray-600">{feature.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Workshop Process */}
      <section className="py-20 bg-white">
        <div className="container">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Atölye Süreçleri</h2>
            <p className="mt-4 text-lg text-gray-600">
              Uçuş Atölyesi'nde eğitim sürecinin nasıl ilerlediğini keşfedin.
            </p>
          </div>
          <div className="relative">
            <div className="absolute left-1/2 -ml-0.5 w-0.5 h-full bg-sky-200"></div>
            {[
              {
                title: "Tanışma ve Oryantasyon",
                description: "Öğrenciler ve eğitmenler tanışır, atölye kuralları ve güvenlik önlemleri anlatılır.",
                icon: <Users className="h-8 w-8 text-white" />,
              },
              {
                title: "Teorik Eğitim",
                description: "Drone teknolojisi, uçuş prensipleri ve temel kavramlar hakkında bilgi verilir.",
                icon: <BookOpen className="h-8 w-8 text-white" />,
              },
              {
                title: "Pratik Uygulama",
                description: "Öğrenciler drone'ları kontrol etmeyi ve temel uçuş manevralarını öğrenir.",
                icon: <Rocket className="h-8 w-8 text-white" />,
              },
              {
                title: "Proje Geliştirme",
                description:
                  "Öğrenciler kendi projelerini geliştirerek yaratıcılıklarını ve problem çözme becerilerini kullanır.",
                icon: <Lightbulb className="h-8 w-8 text-white" />,
              },
              {
                title: "Sunum ve Değerlendirme",
                description: "Öğrenciler projelerini sunar ve eğitmenler tarafından değerlendirilir.",
                icon: <Award className="h-8 w-8 text-white" />,
              },
            ].map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className={`relative mb-12 ${index % 2 === 0 ? "md:pr-12 md:text-right md:ml-auto md:mr-auto md:pl-20" : "md:pl-12 md:ml-auto md:mr-auto md:pr-20"} md:w-1/2`}
              >
                <div
                  className={`absolute top-0 ${
                    index % 2 === 0
                      ? "md:-left-4 left-1/2 -translate-x-1/2 md:translate-x-0"
                      : "md:-right-4 left-1/2 -translate-x-1/2 md:translate-x-0"
                  } w-8 h-8 rounded-full bg-sky-500 flex items-center justify-center z-10`}
                >
                  {step.icon}
                </div>
                <div className="bg-white p-6 rounded-lg shadow-md">
                  <h3 className="text-xl font-semibold mb-2">{step.title}</h3>
                  <p className="text-gray-600">{step.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Education Philosophy */}
      <section className="py-20 bg-sky-600 text-white">
        <div className="container">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative h-[400px] rounded-lg overflow-hidden"
            >
              <Image src="/about-philosophy.jpg" alt="STEM Eğitimi" fill className="object-cover" />
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">STEM Odaklı Eğitim Felsefemiz</h2>
              <div className="mt-6 space-y-6 text-lg text-white/90">
                <p>
                  Uçuş Atölyesi olarak, STEM (Bilim, Teknoloji, Mühendislik ve Matematik) eğitim yaklaşımını
                  benimsiyoruz. Bu yaklaşım, çocukların teorik bilgiyi pratik uygulamalarla pekiştirmelerini sağlar.
                </p>
                <p>
                  Drone teknolojisi, STEM eğitimi için mükemmel bir araçtır. Öğrenciler, fizik prensiplerini, elektronik
                  bileşenleri, programlama ve matematiksel hesaplamaları gerçek dünya uygulamalarıyla öğrenirler.
                </p>
                <p>
                  Eğitim programlarımızda, çocukların merak duygusunu canlı tutmayı, sorgulama becerilerini geliştirmeyi
                  ve takım çalışmasını teşvik etmeyi hedefliyoruz.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
