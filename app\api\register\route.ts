import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Registration from "@/lib/models/registration"
import { handleApiError } from "@/lib/api-utils"

export async function POST(request: Request) {
  try {
    const body = await request.json()

    // Connect to the database
    await dbConnect()

    // Create a new registration
    const registration = await Registration.create(body)

    return NextResponse.json({ success: true, data: registration }, { status: 201 })
  } catch (error) {
    console.error("Registration error:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
