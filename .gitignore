# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*
!.env.production

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# logs
logs/
*.log

# uploads
/public/uploads/*
!/public/uploads/.gitkeep

# PM2
.pm2/

# Docker
.dockerignore
