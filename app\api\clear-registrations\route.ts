import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import mongoose from "mongoose"

export async function POST() {
  try {
    await dbConnect()
    
    // Registration collection'ını tamamen sil
    await mongoose.connection.db.collection('registrations').drop()
    console.log("Registrations collection dropped")
    
    // Model cache'ini temizle
    if (mongoose.models.Registration) {
      delete mongoose.models.Registration
    }
    
    return NextResponse.json({
      success: true,
      message: "Registrations collection cleared successfully"
    })
    
  } catch (error) {
    console.error("Clear registrations error:", error)
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
