import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Testimonial from "@/lib/models/testimonial"
import { handleApiError } from "@/lib/api-utils"

export async function GET(request: Request) {
  try {
    await dbConnect()

    const { searchParams } = new URL(request.url)
    const status = searchParams.get("status")
    const search = searchParams.get("search")

    // Build filter
    const filter: any = {}
    if (status && status !== "all") {
      filter.status = status
    }
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: "i" } },
        { quote: { $regex: search, $options: "i" } },
      ]
    }

    const testimonials = await Testimonial.find(filter).sort({ createdAt: -1 })

    return NextResponse.json({ success: true, data: testimonials })
  } catch (error) {
    console.error("Error fetching testimonials:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()

    await dbConnect()

    const testimonial = await Testimonial.create(body)

    return NextResponse.json({ success: true, data: testimonial }, { status: 201 })
  } catch (error) {
    console.error("Error creating testimonial:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
