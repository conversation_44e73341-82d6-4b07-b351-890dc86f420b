import mongoose from "mongoose"
import bcrypt from "bcryptjs"

// Define the schema
const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "İsim gereklidir"],
      trim: true,
    },
    email: {
      type: String,
      required: [true, "E-posta gereklidir"],
      unique: true,
      trim: true,
      lowercase: true,
      match: [/^\S+@\S+\.\S+$/, "Geçerli bir e-posta adresi giriniz"],
    },
    password: {
      type: String,
      required: [true, "Şifre gereklidir"],
      minlength: [6, "Şifre en az 6 karakter olmalıdır"],
    },
    role: {
      type: String,
      enum: ["admin", "editor"],
      default: "editor",
    },
    profileImage: {
      type: String,
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  },
)

// Hash password before saving (sadece plain text şifreler için)
userSchema.pre("save", async function (next) {
  if (!this.isModified("password")) {
    return next()
  }

  // Eğer şifre zaten hash'lenmişse ($ ile başlıyorsa) tekrar hash'leme
  if (this.password.startsWith('$2b$') || this.password.startsWith('$2a$')) {
    return next()
  }

  try {
    const salt = await bcrypt.genSalt(10)
    this.password = await bcrypt.hash(this.password, salt)
    next()
  } catch (error) {
    next(error)
  }
})

// Method to compare password
userSchema.methods.comparePassword = async function (candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password)
}

// Check if the model already exists to prevent overwriting
export default mongoose.models.User || mongoose.model("User", userSchema)
