version: '3.8'

services:
  ucus-atolyesi:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=******************************************************************
      - NEXTAUTH_SECRET=d055581db8e08ea65c6ba9da9958f9fb1146796bbb3088a7bedb83f7ba7d4673
      - NEXTAUTH_URL=https://www.ucusatolyesi.com.tr
      - ENABLE_API_DOCS=false
      - ENABLE_SECURITY_LOGS=true
      - ENABLE_RATE_LIMIT=true
    restart: unless-stopped
    volumes:
      - ./public/uploads:/app/public/uploads
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
