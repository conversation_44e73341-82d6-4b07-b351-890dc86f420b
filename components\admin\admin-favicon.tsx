"use client"

import { useEffect } from "react"

export function AdminFavicon() {
  useEffect(() => {
    const createAdminFavicon = () => {
      // Mevcut dinamik favicon'ları temizle
      const existingDynamicFavicons = document.querySelectorAll('link[data-dynamic="true"]')
      existingDynamicFavicons.forEach(link => link.remove())

      // Canvas ile admin favicon oluştur
      const canvas = document.createElement('canvas')
      canvas.width = 32
      canvas.height = 32
      const ctx = canvas.getContext('2d')

      if (ctx) {
        // Dış çerçeve (border) - admin için koyu kırmızı
        ctx.fillStyle = '#991b1b' // red-800 (daha koyu çerçeve)
        ctx.beginPath()
        ctx.arc(16, 16, 16, 0, 2 * Math.PI)
        ctx.fill()

        // İç daire arka plan (admin için kırmızı)
        ctx.fillStyle = '#dc2626' // red-600
        ctx.beginPath()
        ctx.arc(16, 16, 13, 0, 2 * Math.PI) // 3px çerçeve için 13px radius
        ctx.fill()

        // Emoji
        ctx.font = '14px Arial'
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        ctx.fillStyle = '#ffffff'
        ctx.fillText('⚙️', 16, 16)

        // Canvas'ı favicon olarak ayarla
        const link = document.createElement('link')
        link.rel = 'icon'
        link.type = 'image/png'
        link.href = canvas.toDataURL()
        link.setAttribute('data-dynamic', 'true')
        link.setAttribute('data-admin', 'true')
        document.head.appendChild(link)

        console.log("Admin Favicon: Daire çerçeveli admin favicon oluşturuldu")
      }
    }

    const updatePageTitle = () => {
      // Admin sayfalarında başlığa "Admin" ekle
      if (!document.title.includes("Admin")) {
        document.title = "Admin - " + document.title.replace("Admin - ", "")
      }
    }

    createAdminFavicon()
    updatePageTitle()

    // Cleanup function - admin sayfasından çıkıldığında normal favicon'a dön
    return () => {
      const adminFavicons = document.querySelectorAll('link[data-admin="true"]')
      adminFavicons.forEach(link => link.remove())
      
      // Normal favicon'u geri yükle
      const event = new CustomEvent('restoreFavicon')
      window.dispatchEvent(event)
    }
  }, [])

  return null
}
