import mongoose from "mongoose"

const contactSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Ad Soyad gereklidir"],
      trim: true,
    },
    email: {
      type: String,
      required: [true, "E-posta gereklidir"],
      trim: true,
      lowercase: true,
    },
    phone: {
      type: String,
      trim: true,
    },
    subject: {
      type: String,
      required: [true, "Konu gereklidir"],
      trim: true,
    },
    message: {
      type: String,
      required: [true, "Mesaj gereklidir"],
      trim: true,
    },
    status: {
      type: String,
      enum: ["new", "read", "replied", "closed"],
      default: "new",
    },
    isRead: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  }
)

// Index for better performance
contactSchema.index({ status: 1 })
contactSchema.index({ isRead: 1 })
contactSchema.index({ createdAt: -1 })

const Contact = mongoose.models.Contact || mongoose.model("Contact", contactSchema)

export default Contact
