import mongoose from "mongoose"

// Site Settings Schema
const siteSettingsSchema = new mongoose.Schema(
  {
    // Site Genel Bilgileri
    siteName: {
      type: String,
      default: "Uçuş Atölyesi",
      trim: true,
    },
    siteDescription: {
      type: String,
      default: "Çocuklar için STEM tabanlı uçuş ve teknoloji atölyesi",
      trim: true,
    },
    siteLogo: {
      type: String,
      trim: true,
    },
    
    // İletişim Bilgileri
    contact: {
      phone: {
        type: String,
        trim: true,
      },
      email: {
        type: String,
        trim: true,
      },
      address: {
        type: String,
        trim: true,
      },
      workingHours: {
        type: String,
        default: "Pazartesi - Cuma: 09:00 - 18:00",
        trim: true,
      },
    },

    // Sosyal Medya Linkleri
    socialMedia: {
      facebook: {
        type: String,
        trim: true,
      },
      instagram: {
        type: String,
        trim: true,
      },
      twitter: {
        type: String,
        trim: true,
      },
      youtube: {
        type: String,
        trim: true,
      },
      linkedin: {
        type: String,
        trim: true,
      },
      tiktok: {
        type: String,
        trim: true,
      },
    },

    // Footer Bölümleri
    footer: {
      aboutText: {
        type: String,
        default: "Uçuş Atölyesi, çocuklara drone teknolojisi ve havacılık bilimini eğlenceli ve eğitici bir ortamda öğreten bir STEM eğitim merkezidir.",
        trim: true,
      },
      quickLinks: [
        {
          title: {
            type: String,
            trim: true,
          },
          url: {
            type: String,
            trim: true,
          },
          isActive: {
            type: Boolean,
            default: true,
          },
        }
      ],
      services: [
        {
          title: {
            type: String,
            trim: true,
          },
          url: {
            type: String,
            trim: true,
          },
          isActive: {
            type: Boolean,
            default: true,
          },
        }
      ],
      copyrightText: {
        type: String,
        default: "© 2024 Uçuş Atölyesi. Tüm hakları saklıdır.",
        trim: true,
      },
    },

    // SEO Ayarları
    seo: {
      metaTitle: {
        type: String,
        trim: true,
      },
      metaDescription: {
        type: String,
        trim: true,
      },
      metaKeywords: {
        type: String,
        trim: true,
      },
      ogImage: {
        type: String,
        trim: true,
      },
    },

    // Diğer Ayarlar
    settings: {
      maintenanceMode: {
        type: Boolean,
        default: false,
      },
      allowRegistrations: {
        type: Boolean,
        default: true,
      },
      allowTestimonials: {
        type: Boolean,
        default: true,
      },
      allowComments: {
        type: Boolean,
        default: true,
      },
    },

    // Tek kayıt olması için
    isDefault: {
      type: Boolean,
      default: true,
      unique: true,
    },
  },
  {
    timestamps: true,
  }
)

// Check if the model already exists to prevent overwriting
export default mongoose.models.SiteSettings || mongoose.model("SiteSettings", siteSettingsSchema)
