// <PERSON><PERSON> <PERSON>ya, MongoDB bağlantısı olmadığında kullanılacak sahte kimlik doğrulama işlevleri sağlar

// Sahte kullanıcı veritabanı
const mockUsers = [
  {
    id: "mock-admin-id",
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    password: "admin123", // Gerçek uygulamada şifreler asla açık metin olarak saklanmamalıdır
    role: "admin",
    isActive: true,
  },
  {
    id: "mock-editor-id",
    name: "<PERSON><PERSON><PERSON>llan<PERSON>",
    email: "<EMAIL>",
    password: "editor123",
    role: "editor",
    isActive: true,
  },
]

// Sahte kimlik doğrulama fonksiyonu
export async function mockAuthenticateUser(email: string, password: string) {
  // Kullanıcıyı bul
  const user = mockUsers.find((u) => u.email === email)

  // Kullanıcı bulunamadıysa veya şifre yanlışsa null döndür
  if (!user || user.password !== password) {
    return null
  }

  // Kullanıcı bilgilerini döndür (şifre hariç)
  const { password: _, ...userWithoutPassword } = user
  return userWithoutPassword
}
