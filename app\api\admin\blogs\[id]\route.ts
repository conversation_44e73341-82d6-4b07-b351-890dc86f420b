import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Blog from "@/lib/models/blog"
import { handleApiError } from "@/lib/api-utils"

export async function GET(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    await dbConnect()

    const { id } = await params
    const blog = await Blog.findById(id)

    if (!blog) {
      return NextResponse.json({ success: false, error: "Blog bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: blog })
  } catch (error) {
    console.error("Error fetching blog:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const body = await request.json()
    await dbConnect()

    const { id } = await params

    // Okuma süresini hesapla (yaklaşık)
    if (body.content && !body.readTime) {
      const wordCount = body.content.split(' ').length
      body.readTime = Math.max(1, Math.ceil(wordCount / 200)) // 200 kelime/dakika
    }

    const blog = await Blog.findByIdAndUpdate(id, body, { new: true, runValidators: true })

    if (!blog) {
      return NextResponse.json({ success: false, error: "Blog bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: blog })
  } catch (error) {
    console.error("Error updating blog:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    await dbConnect()

    const { id } = await params
    const blog = await Blog.findByIdAndDelete(id)

    if (!blog) {
      return NextResponse.json({ success: false, error: "Blog bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, message: "Blog başarıyla silindi" })
  } catch (error) {
    console.error("Error deleting blog:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
