import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Blog from "@/lib/models/blog"

export async function GET(request: Request, { params }: { params: Promise<{ slug: string }> }) {
  try {
    await dbConnect()

    const { slug } = await params
    const blog = await Blog.findOne({
      slug: slug,
      isPublished: true,
      isActive: true
    })

    if (!blog) {
      return NextResponse.json({ success: false, error: "Blog bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: blog })
  } catch (error) {
    console.error("Error fetching public blog:", error)
    return NextResponse.json({ success: false, error: "Blog yüklenirken hata oluştu" }, { status: 500 })
  }
}
