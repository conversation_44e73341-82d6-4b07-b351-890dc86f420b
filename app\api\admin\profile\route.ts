import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import User from "@/lib/models/user"
import { checkAdminAuth, updateDevUserData, getDevUserData } from "@/lib/auth-utils"
import { handleApiError } from "@/lib/api-utils"

export async function GET(request: Request) {
  try {
    // Admin auth kontrolü
    const authCheck = await checkAdminAuth()
    if (!authCheck.authorized) {
      return NextResponse.json({ success: false, error: authCheck.error }, { status: 401 })
    }

    // Development modunda mock data döndür
    if (process.env.NODE_ENV === "development") {
      const devData = getDevUserData()
      return NextResponse.json({
        success: true,
        data: {
          _id: devData?.id || "dev-admin-id",
          name: devData?.name || "Admin",
          email: devData?.email || "<EMAIL>",
          role: devData?.role || "admin",
          profileImage: devData?.image || "",
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
    }

    await dbConnect()

    const user = await User.findById(authCheck.user.id).select('-password')

    if (!user) {
      return NextResponse.json({ success: false, error: "Kullanıcı bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: user })
  } catch (error) {
    console.error("Error fetching profile:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function PUT(request: Request) {
  try {
    // Admin auth kontrolü
    const authCheck = await checkAdminAuth()
    if (!authCheck.authorized) {
      return NextResponse.json({ success: false, error: authCheck.error }, { status: 401 })
    }

    const body = await request.json()
    const { name, email, profileImage } = body

    // Development modunda mock response döndür ve veriyi güncelle
    if (process.env.NODE_ENV === "development") {
      // Dev user data'yı güncelle
      updateDevUserData({
        name: name || authCheck.user.name || "Admin",
        email: email || authCheck.user.email || "<EMAIL>",
        image: profileImage || authCheck.user.image || ""
      })

      const updatedDevData = getDevUserData()
      const updatedUser = {
        _id: updatedDevData?.id || "dev-admin-id",
        name: updatedDevData?.name || "Admin",
        email: updatedDevData?.email || "<EMAIL>",
        role: updatedDevData?.role || "admin",
        profileImage: updatedDevData?.image || "",
        createdAt: new Date(),
        updatedAt: new Date()
      }

      return NextResponse.json({
        success: true,
        data: updatedUser
      })
    }

    await dbConnect()

    // E-posta benzersizlik kontrolü (kendi e-postası hariç)
    if (email) {
      const existingUser = await User.findOne({
        email,
        _id: { $ne: authCheck.user.id }
      })

      if (existingUser) {
        return NextResponse.json({
          success: false,
          error: "Bu e-posta adresi zaten kullanılıyor"
        }, { status: 400 })
      }
    }

    const updatedUser = await User.findByIdAndUpdate(
      authCheck.user.id,
      {
        ...(name && { name }),
        ...(email && { email }),
        ...(profileImage !== undefined && { profileImage }),
        updatedAt: new Date(),
      },
      { new: true, select: '-password' }
    )

    if (!updatedUser) {
      return NextResponse.json({ success: false, error: "Kullanıcı bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: updatedUser })
  } catch (error) {
    console.error("Error updating profile:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
