"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Star } from "lucide-react"
import { toast } from "sonner"

export function TestimonialForm() {
  const [formData, setFormData] = useState({
    name: "",
    role: "",
    quote: "",
    rating: 5,
    program: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleRatingChange = (rating: number) => {
    setFormData((prev) => ({ ...prev, rating }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await fetch("/api/testimonials", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (data.success) {
        toast.success(data.message || "Yorumunuz başarıyla gönderildi!")
        setFormData({
          name: "",
          role: "",
          quote: "",
          rating: 5,
          program: "",
        })
      } else {
        toast.error(data.error || "Yorum gönderilirken hata oluştu")
      }
    } catch (error) {
      console.error("Error submitting testimonial:", error)
      toast.error("Yorum gönderilirken hata oluştu")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Deneyiminizi Paylaşın</CardTitle>
        <CardDescription>
          Uçuş Atölyesi hakkındaki görüşlerinizi bizimle paylaşın. Yorumunuz admin onayından sonra yayınlanacaktır.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Ad Soyad *</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Adınız ve soyadınız"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="role">Rol</Label>
              <Input
                id="role"
                name="role"
                value={formData.role}
                onChange={handleChange}
                placeholder="Örn: Ali'nin annesi"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="program">Program</Label>
            <Select value={formData.program} onValueChange={(value) => setFormData((prev) => ({ ...prev, program: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Katıldığınız programı seçin" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="UA1">UA1 - Giriş Seviyesi</SelectItem>
                <SelectItem value="UA2">UA2 - Orta Seviye</SelectItem>
                <SelectItem value="UA3">UA3 - İleri Seviye</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Değerlendirme</Label>
            <div className="flex gap-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => handleRatingChange(star)}
                  className="p-1 hover:scale-110 transition-transform"
                >
                  <Star
                    className={`h-6 w-6 ${
                      star <= formData.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                    }`}
                  />
                </button>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="quote">Yorumunuz *</Label>
            <Textarea
              id="quote"
              name="quote"
              value={formData.quote}
              onChange={handleChange}
              placeholder="Deneyiminizi ve görüşlerinizi paylaşın..."
              rows={4}
              required
            />
          </div>

          <Button type="submit" disabled={isSubmitting} className="w-full bg-sky-500 hover:bg-sky-600">
            {isSubmitting ? "Gönderiliyor..." : "Yorumu Gönder"}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
