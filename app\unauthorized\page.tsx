"use client"

import { useSearchPara<PERSON>, useRouter } from 'next/navigation'
import { useEffect, useState, Suspense } from 'react'
import Link from 'next/link'
import { AlertTriangle, Home, Lock, FileText, Shield, ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

// SearchParams kullanan bileşeni ayrı bir component olarak çıkarıyoruz
function UnauthorizedContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [reason, setReason] = useState('')
  const [countdown, setCountdown] = useState(10)

  useEffect(() => {
    const reasonParam = searchParams.get('reason')
    setReason(reasonParam || 'unknown')
  }, [searchParams])

  useEffect(() => {
    // 10 saniye sonra ana sayfaya yönlendir
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          router.push('/')
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [router])

  const getReasonInfo = (reason: string) => {
    switch (reason) {
      case 'api-docs':
        return {
          title: 'API Dokümantasyonuna Erişim Engellendi',
          description: 'API dokümantasyonu sadece sistem yöneticileri tarafından görüntülenebilir.',
          icon: <FileText className="h-16 w-16 text-red-500" />,
          details: 'Bu sayfa hassas sistem bilgileri içerdiği için sadece yetkili personel erişebilir.'
        }
      case 'admin':
        return {
          title: 'Yönetici Paneline Erişim Engellendi',
          description: 'Bu alan sadece sistem yöneticileri için tasarlanmıştır.',
          icon: <Shield className="h-16 w-16 text-red-500" />,
          details: 'Yönetici paneline erişim için gerekli yetkilere sahip değilsiniz.'
        }
      default:
        return {
          title: 'Yetkisiz Erişim Denemesi',
          description: 'Bu sayfaya erişim yetkiniz bulunmamaktadır.',
          icon: <Lock className="h-16 w-16 text-red-500" />,
          details: 'Erişmeye çalıştığınız sayfa korumalı bir alandır.'
        }
    }
  }

  const reasonInfo = getReasonInfo(reason)

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl shadow-2xl border-red-200">
        <CardHeader className="text-center pb-2">
          <div className="flex justify-center mb-4">
            {reasonInfo.icon}
          </div>
          <CardTitle className="text-3xl font-bold text-red-700 mb-2">
            {reasonInfo.title}
          </CardTitle>
          <CardDescription className="text-lg text-red-600">
            {reasonInfo.description}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-red-800 mb-1">Güvenlik Uyarısı</h3>
                <p className="text-red-700 text-sm">
                  {reasonInfo.details}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-800 mb-2">Ne Yapabilirsiniz?</h3>
            <ul className="text-blue-700 text-sm space-y-1">
              <li>• Ana sayfaya geri dönebilirsiniz</li>
              <li>• Eğer yönetici iseniz, giriş yaparak tekrar deneyebilirsiniz</li>
              <li>• Yardım için iletişim sayfasından bize ulaşabilirsiniz</li>
            </ul>
          </div>

          <div className="text-center">
            <div className="bg-gray-100 rounded-lg p-4 mb-4">
              <p className="text-gray-600 text-sm mb-2">
                Otomatik yönlendirme
              </p>
              <p className="text-2xl font-bold text-gray-800">
                {countdown} saniye
              </p>
              <p className="text-gray-500 text-xs">
                Ana sayfaya otomatik olarak yönlendirileceksiniz
              </p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button 
              onClick={() => router.back()} 
              variant="outline" 
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Geri Dön
            </Button>
            
            <Button asChild className="flex items-center gap-2">
              <Link href="/">
                <Home className="h-4 w-4" />
                Ana Sayfa
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="flex items-center gap-2">
              <Link href="/contact">
                <AlertTriangle className="h-4 w-4" />
                İletişim
              </Link>
            </Button>
          </div>

          <div className="text-center pt-4 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              Bu güvenlik önlemi sitenin güvenliğini sağlamak için alınmıştır.
              <br />
              Anlayışınız için teşekkür ederiz.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Loading bileşeni
function UnauthorizedLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl shadow-2xl border-red-200">
        <CardHeader className="text-center pb-2">
          <div className="flex justify-center mb-4">
            <Lock className="h-16 w-16 text-red-500" />
          </div>
          <CardTitle className="text-3xl font-bold text-red-700 mb-2">
            Yükleniyor...
          </CardTitle>
          <CardDescription className="text-lg text-red-600">
            Sayfa bilgileri yükleniyor.
          </CardDescription>
        </CardHeader>
      </Card>
    </div>
  )
}

// Ana bileşen - Suspense ile sarılmış
export default function UnauthorizedPage() {
  return (
    <Suspense fallback={<UnauthorizedLoading />}>
      <UnauthorizedContent />
    </Suspense>
  )
}
