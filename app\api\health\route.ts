import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"

export async function GET() {
  try {
    // Veritabanı bağlantısını test et
    await dbConnect()
    
    return NextResponse.json({
      status: "healthy",
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      database: "connected",
      version: "1.0.0"
    })
  } catch (error) {
    console.error("Health check failed:", error)
    
    return NextResponse.json({
      status: "unhealthy",
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      database: "disconnected",
      error: error.message
    }, { status: 503 })
  }
}
