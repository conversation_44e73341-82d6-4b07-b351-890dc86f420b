"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useSession } from "next-auth/react"
import {
  LayoutDashboard,
  GraduationCap,
  Users,
  Image,
  FileText,
  MessageSquare,
  Mail,
  Settings,
  ExternalLink,
  ChevronLeft,
  ChevronRight,
  LogOut,
  UserCircle,
} from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useState, useEffect } from "react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Menu } from "lucide-react"

const navItems = [
  {
    title: "Dashboard",
    href: "/admin/dashboard",
    icon: <LayoutDashboard className="h-5 w-5" />,
  },
  {
    title: "Programlar",
    href: "/admin/programs",
    icon: <GraduationCap className="h-5 w-5" />,
  },
  {
    title: "Eğitmenler",
    href: "/admin/instructors",
    icon: <Users className="h-5 w-5" />,
  },
  {
    title: "Galeri",
    href: "/admin/gallery",
    icon: <Image className="h-5 w-5" />,
  },
  {
    title: "Blog",
    href: "/admin/blogs",
    icon: <FileText className="h-5 w-5" />,
  },
  {
    title: "Yorumlar",
    href: "/admin/testimonials",
    icon: <MessageSquare className="h-5 w-5" />,
  },
  {
    title: "Kayıtlar",
    href: "/admin/registrations",
    icon: <Users className="h-5 w-5" />,
  },
  {
    title: "İletişim",
    href: "/admin/contacts",
    icon: <Mail className="h-5 w-5" />,
  },
  {
    title: "Site Ayarları",
    href: "/admin/site-settings",
    icon: <Settings className="h-5 w-5" />,
  },
  {
    title: "API Docs",
    href: "/api/docs",
    icon: <FileText className="h-5 w-5" />,
    external: true,
    adminOnly: true, // Sadece admin'ler görebilir
  },
]

interface AdminSidebarProps {
  user?: any
}

export function AdminSidebar({ user }: AdminSidebarProps) {
  const pathname = usePathname()
  const { data: session } = useSession()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileOpen, setIsMobileOpen] = useState(false)
  const [updatedUser, setUpdatedUser] = useState(null)

  // localStorage'dan dev user data'yı yükle
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      const stored = localStorage.getItem('devUserData')
      if (stored) {
        try {
          const devData = JSON.parse(stored)
          setUpdatedUser(devData)
        } catch (error) {
          console.error("AdminSidebar: Error loading dev user data:", error)
        }
      }
    }
  }, [])

  // Profil güncelleme event'ini dinle
  useEffect(() => {
    const handleProfileUpdate = (event) => {
      setUpdatedUser({
        ...currentUser,
        name: event.detail.name,
        email: event.detail.email,
        image: event.detail.image
      })
    }

    window.addEventListener('profileUpdated', handleProfileUpdate)
    
    return () => {
      window.removeEventListener('profileUpdated', handleProfileUpdate)
    }
  }, [])

  // Session'dan güncel user bilgisini al
  const currentUser = updatedUser || session?.user || user

  const handleLogout = async () => {
    // Geliştirme modunda sadece ana sayfaya yönlendir
    if (process.env.NODE_ENV === "development") {
      window.location.href = "/"
      return
    }
    
    // Production'da NextAuth signOut kullan
    const { signOut } = await import("next-auth/react")
    await signOut({ callbackUrl: "/" })
  }

  return (
    <>
      {/* Mobile Header */}
      <div className="lg:hidden flex items-center justify-between p-4 bg-white border-b border-gray-200">
        <Link href="/admin/dashboard" className="flex items-center gap-2">
          <div className="w-8 h-8 rounded-full bg-sky-100 border-2 border-sky-200 shadow-sm flex items-center justify-center">
            <span className="text-sky-600 font-bold text-sm">🚁</span>
          </div>
          <span className="text-lg font-bold text-sky-600">Uçuş Atölyesi</span>
        </Link>
        <Sheet open={isMobileOpen} onOpenChange={setIsMobileOpen}>
          <SheetTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <Menu className="h-4 w-4" />
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-64 p-0">
            <div className="flex flex-col h-full bg-white">
              {/* Mobile Header */}
              <div className="flex items-center p-4 border-b border-gray-200">
                <Link href="/admin/dashboard" className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full bg-sky-100 border-2 border-sky-200 shadow-sm flex items-center justify-center">
                    <span className="text-sky-600 font-bold text-sm">🚁</span>
                  </div>
                  <span className="text-lg font-bold text-sky-600">Uçuş Atölyesi</span>
                </Link>
              </div>

              {/* Mobile Navigation */}
              <nav className="flex-1 p-4 space-y-2">
                {navItems
                  .filter(item => !item.adminOnly || currentUser?.role === 'admin')
                  .map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    target={item.external ? "_blank" : undefined}
                    rel={item.external ? "noopener noreferrer" : undefined}
                    className={cn(
                      "flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                      pathname === item.href
                        ? "bg-sky-100 text-sky-700 shadow-sm"
                        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                    )}
                    onClick={() => setIsMobileOpen(false)}
                  >
                    {item.icon}
                    <span>{item.title}</span>
                    {item.external && <ExternalLink className="h-3 w-3 ml-auto" />}
                  </Link>
                ))}
              </nav>

              {/* Mobile User Profile */}
              <div className="p-4 border-t border-gray-200">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="w-full justify-start gap-3 h-auto p-3">
                      <Avatar className="h-8 w-8">
                        {currentUser?.image ? (
                          <AvatarImage src={currentUser.image} alt={currentUser?.name || "Admin"} />
                        ) : null}
                        <AvatarFallback className="text-sm">
                          {currentUser?.name ? currentUser.name.split(' ').map(n => n.charAt(0)).join('').toUpperCase().slice(0, 2) : "AD"}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col items-start text-left">
                        <span className="text-sm font-medium">{currentUser?.name || "Admin"}</span>
                        <span className="text-xs text-gray-500">{currentUser?.role || "admin"}</span>
                      </div>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel>
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">{currentUser?.name || "Admin"}</p>
                        <p className="text-xs leading-none text-muted-foreground">{currentUser?.email || "<EMAIL>"}</p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/admin/profile" className="flex items-center">
                        <UserCircle className="h-4 w-4 mr-2" />
                        Profil Ayarları
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout} className="text-red-600 focus:text-red-600">
                      <LogOut className="h-4 w-4 mr-2" />
                      Çıkış Yap
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </SheetContent>
        </Sheet>
      </div>

      {/* Desktop Sidebar */}
      <div className={cn(
        "hidden lg:flex flex-col h-screen bg-white border-r border-gray-200 shadow-sm transition-all duration-300",
        isCollapsed ? "w-16" : "w-64"
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          {!isCollapsed && (
            <Link href="/admin/dashboard" className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-full bg-sky-100 border-2 border-sky-200 shadow-sm flex items-center justify-center">
                <span className="text-sky-600 font-bold text-sm">🚁</span>
              </div>
              <span className="text-lg font-bold text-sky-600">Uçuş Atölyesi</span>
            </Link>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="h-8 w-8 p-0"
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navItems
          .filter(item => !item.adminOnly || currentUser?.role === 'admin')
          .map((item) => (
          <Link
            key={item.href}
            href={item.href}
            target={item.external ? "_blank" : undefined}
            rel={item.external ? "noopener noreferrer" : undefined}
            className={cn(
              "flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 group",
              pathname === item.href
                ? "bg-sky-100 text-sky-700 shadow-sm"
                : "text-gray-600 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm"
            )}
            title={isCollapsed ? item.title : undefined}
          >
            {item.icon}
            {!isCollapsed && (
              <>
                <span>{item.title}</span>
                {item.external && <ExternalLink className="h-3 w-3 ml-auto" />}
              </>
            )}
          </Link>
        ))}
      </nav>

      {/* User Profile */}
      <div className="p-4 border-t border-gray-200">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start gap-3 h-auto p-3",
                isCollapsed && "justify-center"
              )}
            >
              <Avatar className="h-8 w-8">
                {currentUser?.image ? (
                  <AvatarImage src={currentUser.image} alt={currentUser?.name || "Admin"} />
                ) : null}
                <AvatarFallback className="text-sm">
                  {currentUser?.name ? currentUser.name.split(' ').map(n => n.charAt(0)).join('').toUpperCase().slice(0, 2) : "AD"}
                </AvatarFallback>
              </Avatar>
              {!isCollapsed && (
                <div className="flex flex-col items-start text-left">
                  <span className="text-sm font-medium">{currentUser?.name || "Admin"}</span>
                  <span className="text-xs text-gray-500">{currentUser?.role || "admin"}</span>
                </div>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">{currentUser?.name || "Admin"}</p>
                <p className="text-xs leading-none text-muted-foreground">{currentUser?.email || "<EMAIL>"}</p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href="/admin/profile" className="flex items-center">
                <UserCircle className="h-4 w-4 mr-2" />
                Profil Ayarları
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleLogout} className="text-red-600 focus:text-red-600">
              <LogOut className="h-4 w-4 mr-2" />
              Çıkış Yap
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      </div>
    </>
  )
}
