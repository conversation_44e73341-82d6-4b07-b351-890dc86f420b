"use client"

import dynamic from 'next/dynamic'
import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { suppressSwaggerWarnings, restoreConsoleError } from '@/lib/console-filter'

// Swagger UI'yi dinamik <PERSON>ükle (SSR'yi devre dışı bırak)
const SwaggerUI = dynamic(() => import('swagger-ui-react'), {
  ssr: false,
  loading: () => (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-sky-500 mx-auto"></div>
        <p className="mt-4 text-gray-600">Swagger UI yükleniyor...</p>
      </div>
    </div>
  )
})

// Swagger UI'yi StrictMode olmadan render eden wrapper
function SwaggerUIWrapper({ spec }) {
  if (!spec) return null

  return (
    <SwaggerUI
      spec={spec}
      docExpansion="list"
      defaultModelsExpandDepth={2}
      defaultModelExpandDepth={2}
      displayRequestDuration={true}
      tryItOutEnabled={true}
      filter={true}
      showExtensions={true}
      showCommonExtensions={true}
      // Swagger UI konfigürasyonu
      supportedSubmitMethods={['get', 'post', 'put', 'delete', 'patch']}
      validatorUrl={null}
    />
  )
}

export default function ApiDocsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [swaggerSpec, setSwaggerSpec] = useState(null)
  const [isAuthorized, setIsAuthorized] = useState(false)

  useEffect(() => {
    // Geliştirme modunda her zaman izin ver
    if (process.env.NODE_ENV === "development") {
      setIsAuthorized(true)
      loadSwaggerSpec()
      return
    }

    // Auth kontrolü
    if (status === "loading") return // Yükleniyor

    console.log("API Docs Page - Session:", session)
    console.log("API Docs Page - User role:", session?.user?.role)

    if (!session || session.user?.role !== "admin") {
      console.log("API Docs Page - Access denied, redirecting")
      // Admin değilse uyarı sayfasına yönlendir
      router.push("/unauthorized?reason=api-docs")
      return
    }

    console.log("API Docs Page - Access granted")
    setIsAuthorized(true)
    loadSwaggerSpec()
  }, [session, status, router])

  const loadSwaggerSpec = () => {
    // Console warning'leri bastır
    suppressSwaggerWarnings()

    // Swagger spec'i client-side'da yükle
    fetch('/api/docs/swagger.json')
      .then(res => res.json())
      .then(data => setSwaggerSpec(data))
      .catch(err => console.error('Swagger spec yüklenirken hata:', err))
  }

  useEffect(() => {
    // Cleanup function
    return () => {
      restoreConsoleError()
    }
  }, [])

  // Auth kontrolü
  if (!isAuthorized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-sky-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Yetkilendirme kontrol ediliyor...</p>
        </div>
      </div>
    )
  }

  if (!swaggerSpec) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-sky-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">API Dokümantasyonu yükleniyor...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto py-8">
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            🚁 Uçuş Atölyesi API Dokümantasyonu
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Uçuş Atölyesi web sitesi için REST API endpoints'lerinin detaylı dokümantasyonu. 
            Tüm API çağrılarını test edebilir ve entegrasyon için gerekli bilgileri bulabilirsiniz.
          </p>
        </div>
        
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <SwaggerUIWrapper spec={swaggerSpec} />
        </div>
        
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>
            Bu dokümantasyon otomatik olarak oluşturulmuştur. 
            Sorularınız için{' '}
            <a 
              href="mailto:<EMAIL>" 
              className="text-sky-600 hover:text-sky-700"
            >
              <EMAIL>
            </a>
            {' '}adresinden iletişime geçebilirsiniz.
          </p>
        </div>
      </div>
    </div>
  )
}
