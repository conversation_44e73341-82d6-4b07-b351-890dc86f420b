"use client"

import { useEffect } from "react"

interface FaviconUpdaterProps {
  logoUrl?: string
  siteName?: string
}

export function FaviconUpdater({ logoUrl, siteName = "Uçuş Atölyesi" }: FaviconUpdaterProps) {
  useEffect(() => {
    const updateFavicon = () => {
      // Mevcut favicon linklerini temizle
      const existingFavicons = document.querySelectorAll('link[rel*="icon"]')
      existingFavicons.forEach(link => link.remove())

      if (logoUrl) {
        // Logo varsa onu favicon olarak kullan
        const link = document.createElement('link')
        link.rel = 'icon'
        link.type = 'image/x-icon'
        link.href = logoUrl
        document.head.appendChild(link)

        // Apple touch icon için de ekle
        const appleTouchIcon = document.createElement('link')
        appleTouchIcon.rel = 'apple-touch-icon'
        appleTouchIcon.href = logoUrl
        document.head.appendChild(appleTouchIcon)

        console.log("Favicon: Logo favicon olarak ayarlandı:", logoUrl)
      } else {
        // Logo yoksa varsayılan emoji favicon oluştur
        createEmojiFavicon()
      }

      // Sayfa başlığını da güncelle
      if (document.title !== siteName) {
        document.title = siteName
      }
    }

    const createEmojiFavicon = () => {
      // Canvas ile emoji favicon oluştur
      const canvas = document.createElement('canvas')
      canvas.width = 32
      canvas.height = 32
      const ctx = canvas.getContext('2d')

      if (ctx) {
        // Arka plan
        ctx.fillStyle = '#0ea5e9' // sky-500
        ctx.fillRect(0, 0, 32, 32)

        // Emoji
        ctx.font = '20px Arial'
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        ctx.fillStyle = '#ffffff'
        ctx.fillText('🚁', 16, 16)

        // Canvas'ı favicon olarak ayarla
        const link = document.createElement('link')
        link.rel = 'icon'
        link.type = 'image/png'
        link.href = canvas.toDataURL()
        document.head.appendChild(link)

        console.log("Favicon: Emoji favicon oluşturuldu")
      }
    }

    updateFavicon()
  }, [logoUrl, siteName])

  return null // Bu component görsel bir şey render etmez
}
