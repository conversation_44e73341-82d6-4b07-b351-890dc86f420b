import mongoose from "mongoose"

// Define the schema
const registrationSchema = new mongoose.Schema(
  {
    childName: {
      type: String,
      required: [true, "Çocuğun adı soyadı gereklidir"],
      trim: true,
    },
    childAge: {
      type: Number,
      required: [true, "Çocuğun yaşı gereklidir"],
      min: [6, "Minimum yaş 6 olmalıdır"],
      max: [14, "Maksimum yaş 14 olmalıdır"],
    },
    parentName: {
      type: String,
      required: [true, "Veli adı soyadı gereklidir"],
      trim: true,
    },
    email: {
      type: String,
      required: [true, "E-posta adresi gereklidir"],
      trim: true,
      lowercase: true,
      match: [/^\S+@\S+\.\S+$/, "Geçerli bir e-posta adresi giriniz"],
    },
    phone: {
      type: String,
      required: [true, "Telefon numarası gereklidir"],
      trim: true,
    },
    program: {
      type: String,
      required: [true, "Program seçimi gereklidir"],
      enum: ["ua1", "ua2", "ua3"],
    },
    address: {
      type: String,
      required: [true, "Adres gereklidir"],
      trim: true,
    },
    notes: {
      type: String,
      trim: true,
    },
    status: {
      type: String,
      enum: ["pending", "approved", "rejected"],
      default: "pending",
    },
  },
  {
    timestamps: true,
  },
)

// Check if the model already exists to prevent overwriting
export default mongoose.models.Registration || mongoose.model("Registration", registrationSchema)
