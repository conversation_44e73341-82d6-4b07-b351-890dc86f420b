import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Testimonial from "@/lib/models/testimonial"
import { handleApiError } from "@/lib/api-utils"

export async function GET(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    await dbConnect()

    const { id } = await params
    const testimonial = await Testimonial.findById(id)

    if (!testimonial) {
      return NextResponse.json({ success: false, error: "Yorum bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: testimonial })
  } catch (error) {
    console.error("Error fetching testimonial:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const body = await request.json()

    await dbConnect()

    const { id } = await params
    const testimonial = await Testimonial.findByIdAndUpdate(id, body, {
      new: true,
      runValidators: true,
    })

    if (!testimonial) {
      return NextResponse.json({ success: false, error: "Yorum bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: testimonial })
  } catch (error) {
    console.error("Error updating testimonial:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function DELETE(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    await dbConnect()

    const { id } = await params
    const testimonial = await Testimonial.findByIdAndDelete(id)

    if (!testimonial) {
      return NextResponse.json({ success: false, error: "Yorum bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, message: "Yorum başarıyla silindi" })
  } catch (error) {
    console.error("Error deleting testimonial:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
