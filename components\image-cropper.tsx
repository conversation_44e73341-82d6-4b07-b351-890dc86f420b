"use client"

import { useState, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>lider } from "@/components/ui/slider"
import { Label } from "@/components/ui/label"
import { Crop, RotateCw, ZoomIn, ZoomOut, Check, X } from "lucide-react"

interface ImageCropperProps {
  imageFile: File
  onCropComplete: (croppedFile: File) => void
  onCancel: () => void
  aspectRatio?: number // 1 for square, 16/9 for landscape, etc.
}

export function ImageCropper({ imageFile, onCropComplete, onCancel, aspectRatio = 1 }: ImageCropperProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const [imageUrl, setImageUrl] = useState<string>("")
  const [scale, setScale] = useState([1])
  const [rotation, setRotation] = useState(0)
  const [crop, setCrop] = useState({ x: 0, y: 0, width: 200, height: 200 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })

  // Load image
  useState(() => {
    const url = URL.createObjectURL(imageFile)
    setImageUrl(url)
    return () => URL.revokeObjectURL(url)
  }, [imageFile])

  const handleImageLoad = useCallback(() => {
    if (imageRef.current) {
      const img = imageRef.current
      const size = Math.min(img.naturalWidth, img.naturalHeight)
      setCrop({
        x: (img.naturalWidth - size) / 2,
        y: (img.naturalHeight - size) / 2,
        width: size,
        height: size / aspectRatio
      })
    }
  }, [aspectRatio])

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setDragStart({ x: e.clientX - crop.x, y: e.clientY - crop.y })
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !imageRef.current) return
    
    const newX = e.clientX - dragStart.x
    const newY = e.clientY - dragStart.y
    
    setCrop(prev => ({
      ...prev,
      x: Math.max(0, Math.min(newX, imageRef.current!.naturalWidth - prev.width)),
      y: Math.max(0, Math.min(newY, imageRef.current!.naturalHeight - prev.height))
    }))
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const handleCrop = async () => {
    if (!imageRef.current || !canvasRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const img = imageRef.current
    const scaleValue = scale[0]

    // Set canvas size
    canvas.width = crop.width
    canvas.height = crop.height

    // Apply transformations
    ctx.save()
    ctx.translate(canvas.width / 2, canvas.height / 2)
    ctx.rotate((rotation * Math.PI) / 180)
    ctx.scale(scaleValue, scaleValue)
    ctx.translate(-canvas.width / 2, -canvas.height / 2)

    // Draw cropped image
    ctx.drawImage(
      img,
      crop.x, crop.y, crop.width, crop.height,
      0, 0, canvas.width, canvas.height
    )
    ctx.restore()

    // Convert to blob
    canvas.toBlob((blob) => {
      if (blob) {
        const croppedFile = new File([blob], imageFile.name, {
          type: imageFile.type,
          lastModified: Date.now()
        })
        onCropComplete(croppedFile)
      }
    }, imageFile.type, 0.9)
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Crop className="h-5 w-5" />
          Resim Düzenle
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Image Preview */}
        <div className="relative border rounded-lg overflow-hidden bg-gray-100">
          <img
            ref={imageRef}
            src={imageUrl}
            alt="Crop preview"
            className="max-w-full max-h-96 mx-auto"
            onLoad={handleImageLoad}
            style={{
              transform: `scale(${scale[0]}) rotate(${rotation}deg)`
            }}
          />
          
          {/* Crop overlay */}
          <div
            className="absolute border-2 border-sky-500 bg-sky-500/20 cursor-move"
            style={{
              left: crop.x,
              top: crop.y,
              width: crop.width,
              height: crop.height
            }}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
          />
        </div>

        {/* Controls */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Scale */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <ZoomIn className="h-4 w-4" />
              Yakınlaştırma: {scale[0].toFixed(1)}x
            </Label>
            <Slider
              value={scale}
              onValueChange={setScale}
              min={0.5}
              max={3}
              step={0.1}
              className="w-full"
            />
          </div>

          {/* Rotation */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <RotateCw className="h-4 w-4" />
              Döndürme: {rotation}°
            </Label>
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setRotation(prev => prev - 90)}
              >
                -90°
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setRotation(prev => prev + 90)}
              >
                +90°
              </Button>
            </div>
          </div>

          {/* Crop Size */}
          <div className="space-y-2">
            <Label>Kırpma Boyutu</Label>
            <div className="text-sm text-gray-600">
              {crop.width} x {crop.height}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 justify-end">
          <Button type="button" variant="outline" onClick={onCancel}>
            <X className="h-4 w-4 mr-2" />
            İptal
          </Button>
          <Button type="button" onClick={handleCrop} className="bg-sky-500 hover:bg-sky-600">
            <Check className="h-4 w-4 mr-2" />
            Uygula
          </Button>
        </div>

        {/* Hidden canvas for cropping */}
        <canvas ref={canvasRef} className="hidden" />
      </CardContent>
    </Card>
  )
}
