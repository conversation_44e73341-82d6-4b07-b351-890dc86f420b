import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Gallery from "@/lib/models/gallery"
import { handleApiError } from "@/lib/api-utils"

export async function GET(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    await dbConnect()

    const { id } = await params
    const galleryItem = await Gallery.findById(id)

    if (!galleryItem) {
      return NextResponse.json({ success: false, error: "Galeri öğesi bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: galleryItem })
  } catch (error) {
    console.error("Error fetching gallery item:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const body = await request.json()

    await dbConnect()

    const { id } = await params
    const galleryItem = await Gallery.findByIdAndUpdate(id, body, {
      new: true,
      runValidators: true,
    })

    if (!galleryItem) {
      return NextResponse.json({ success: false, error: "Galeri öğesi bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, data: galleryItem })
  } catch (error) {
    console.error("Error updating gallery item:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}

export async function DELETE(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    await dbConnect()

    const { id } = await params
    const galleryItem = await Gallery.findByIdAndDelete(id)

    if (!galleryItem) {
      return NextResponse.json({ success: false, error: "Galeri öğesi bulunamadı" }, { status: 404 })
    }

    return NextResponse.json({ success: true, message: "Galeri öğesi başarıyla silindi" })
  } catch (error) {
    console.error("Error deleting gallery item:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
