import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import User from "@/lib/models/user"

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json()
    
    console.log("=== LOGIN DEBUG ===")
    console.log("Attempting login for:", email)
    console.log("Environment:", process.env.NODE_ENV)
    console.log("NEXTAUTH_URL:", process.env.NEXTAUTH_URL)
    
    // Database bağlantısı
    await dbConnect()
    console.log("Database connected")
    
    // Kullanıcıyı bul
    const user = await User.findOne({ email: email })
    
    if (!user) {
      console.log("User not found")
      return NextResponse.json({
        success: false,
        error: "User not found",
        debug: {
          searchedEmail: email,
          totalUsers: await User.countDocuments()
        }
      })
    }
    
    console.log("User found:", user.email)
    console.log("User active:", user.isActive)
    console.log("User role:", user.role)
    
    // <PERSON><PERSON><PERSON> k<PERSON>rolü
    const isPasswordValid = await user.comparePassword(password)
    console.log("Password valid:", isPasswordValid)
    
    if (!isPasswordValid) {
      return NextResponse.json({
        success: false,
        error: "Invalid password",
        debug: {
          userFound: true,
          passwordChecked: true,
          passwordValid: false
        }
      })
    }
    
    return NextResponse.json({
      success: true,
      message: "Login would be successful",
      debug: {
        userFound: true,
        passwordValid: true,
        user: {
          id: user._id.toString(),
          name: user.name,
          email: user.email,
          role: user.role,
          isActive: user.isActive
        }
      }
    })
    
  } catch (error) {
    console.error("Login debug error:", error)
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
