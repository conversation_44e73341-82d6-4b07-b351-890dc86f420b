// NextAuth debug ve troubleshooting utilities

export function debugAuthConfig() {
  console.log('=== NextAuth Debug Info ===')
  console.log('NODE_ENV:', process.env.NODE_ENV)
  console.log('NEXTAUTH_URL:', process.env.NEXTAUTH_URL)
  console.log('NEXTAUTH_SECRET exists:', !!process.env.NEXTAUTH_SECRET)
  console.log('NEXTAUTH_SECRET length:', process.env.NEXTAUTH_SECRET?.length || 0)
  
  if (process.env.NEXTAUTH_SECRET) {
    console.log('NEXTAUTH_SECRET first 10 chars:', process.env.NEXTAUTH_SECRET.substring(0, 10) + '...')
  }
  
  console.log('=== End Debug Info ===')
}

export function validateAuthSecret(): { valid: boolean; message: string } {
  const secret = process.env.NEXTAUTH_SECRET
  
  if (!secret) {
    return {
      valid: false,
      message: 'NEXTAUTH_SECRET environment variable is not set'
    }
  }
  
  if (secret === 'your-secret-key-here') {
    return {
      valid: false,
      message: 'NEXTAUTH_SECRET is still using default placeholder value'
    }
  }
  
  if (secret.length < 32) {
    return {
      valid: false,
      message: 'NEXTAUTH_SECRET should be at least 32 characters long'
    }
  }
  
  return {
    valid: true,
    message: 'NEXTAUTH_SECRET is properly configured'
  }
}

export function generateNewSecret(): string {
  // Crypto-safe random string generator
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  
  for (let i = 0; i < 64; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  
  return result
}

export function clearAuthStorage() {
  if (typeof window !== 'undefined') {
    // NextAuth cookies'lerini temizle
    const authCookies = [
      'next-auth.session-token',
      '__Secure-next-auth.session-token',
      'next-auth.csrf-token',
      '__Host-next-auth.csrf-token',
      'next-auth.callback-url',
      '__Secure-next-auth.callback-url'
    ]
    
    authCookies.forEach(cookieName => {
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.localhost;`
    })
    
    // LocalStorage'daki auth verilerini temizle
    const authKeys = Object.keys(localStorage).filter(key => 
      key.includes('auth') || key.includes('session') || key.includes('devUserData')
    )
    
    authKeys.forEach(key => {
      localStorage.removeItem(key)
    })
    
    console.log('Auth storage cleared:', { cookies: authCookies, localStorage: authKeys })
  }
}

// Development modunda auth sorunlarını debug et
export function debugAuthIssues() {
  if (process.env.NODE_ENV === 'development') {
    console.log('=== Auth Troubleshooting ===')
    
    // Secret validation
    const secretValidation = validateAuthSecret()
    console.log('Secret validation:', secretValidation)
    
    // Environment check
    debugAuthConfig()
    
    // Browser storage check
    if (typeof window !== 'undefined') {
      console.log('Current cookies:', document.cookie)
      console.log('LocalStorage auth keys:', 
        Object.keys(localStorage).filter(key => 
          key.includes('auth') || key.includes('session')
        )
      )
    }
    
    console.log('=== End Troubleshooting ===')
  }
}

// Auth sorunları için otomatik düzeltme
export function autoFixAuthIssues(): { fixed: boolean; actions: string[] } {
  const actions: string[] = []
  let fixed = false
  
  // Secret kontrolü
  const secretValidation = validateAuthSecret()
  if (!secretValidation.valid) {
    actions.push(`Secret issue: ${secretValidation.message}`)
    if (process.env.NODE_ENV === 'development') {
      actions.push('Suggested fix: Update NEXTAUTH_SECRET in .env.local')
    }
  }
  
  // Browser storage temizleme
  if (typeof window !== 'undefined') {
    const authCookies = document.cookie.split(';').filter(cookie => 
      cookie.includes('next-auth')
    )
    
    if (authCookies.length > 0) {
      clearAuthStorage()
      actions.push('Cleared stale auth cookies and localStorage')
      fixed = true
    }
  }
  
  return { fixed, actions }
}
