import { NextResponse } from "next/server"
import dbConnect from "@/lib/db-connect"
import Testimonial from "@/lib/models/testimonial"
import { handleApiError } from "@/lib/api-utils"

export async function POST(request: Request) {
  try {
    await dbConnect()

    const body = await request.json()
    const { name, role, quote, rating, program } = body

    // Validation
    if (!name || !quote) {
      return NextResponse.json(
        { success: false, error: "Ad ve yorum alanları gereklidir" },
        { status: 400 }
      )
    }

    const testimonial = await Testimonial.create({
      name,
      role,
      quote,
      rating: rating || 5,
      program,
      status: "pending", // Admin onayı bekliyor
      isActive: false, // Admin onaylayana kadar gö<PERSON>ünmez
    })

    return NextResponse.json({ 
      success: true, 
      message: "Yorumunuz başarıyla gönderildi. Admin onayından sonra yayınlanacaktır.",
      data: testimonial 
    }, { status: 201 })
  } catch (error) {
    console.error("Error creating testimonial:", error)
    const errorResponse = await handleApiError(error)
    return NextResponse.json({ success: false, error: errorResponse.error }, { status: errorResponse.status })
  }
}
